{
  "compilerOptions": {
    "target": "ES2020",
    "jsx": "preserve",
    "lib": [
      "DOM",
      "ES2020"
    ],
    "useDefineForClassFields": true,
    "baseUrl": "./",
    /* modules */
    "module": "ESNext",
    "moduleResolution": "node",
    "paths": {
      "@/*": [
        "src/*"
      ],
      "assets/*": [
        "src/assets/*"
      ],
      "pages/*": [
        "src/pages/*"
      ],
      "components/*": [
        "src/components/*"
      ],
      "service/*": [
        "src/service/*"
      ],
      "store/*": [
        "src/store/*"
      ],
      "utils/*": [
        "src/utils/*"
      ],
      "wolf-static-cpnt": [
        "src/components/wolf-static-cpnt"
      ],
      "wolf-static-cpnt/*": [
        "src/components/wolf-static-cpnt/*"
      ]
    },
    "resolveJsonModule": true,
    "allowImportingTsExtensions": true,
    /* type checking */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noEmit": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "include": [
    "src"
  ]
}
