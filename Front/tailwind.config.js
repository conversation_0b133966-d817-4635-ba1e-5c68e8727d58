import plugin from "tailwindcss/plugin";

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{html,js,ts,jsx,tsx,vue}"],
  theme: {
    spacing: Array.from({ length: 1000 }).reduce((map, _, index) => {
      map[index] = `${index}px`;
      return map;
    }, {}),
    extend: {
      fontSize: ({ theme }) => ({
        ...theme("spacing"),
      }),
    },
  },
  corePlugins: {
    preflight: false,
  },
  plugins: [
    plugin(({ addUtilities }) => {
      addUtilities({
        ".my-flex-ja": { "@apply flex justify-center items-center": {} },
        ".my-flex-jac": {
          "@apply flex justify-center items-center flex-col": {},
        },
        ".my-animate": {
          animation: "bounce 1s infinite",
        },
        ".my-text-overflow": {
          "@apply overflow-hidden whitespace-nowrap text-ellipsis": {},
        },
        ".my-text-45": {
          "@apply text-[rgba(0,0,0,.45)]": {},
        },
      });
    }),
  ],
};
