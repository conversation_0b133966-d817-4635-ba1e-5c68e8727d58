<script>
export default {
  name: 'InputNumberWithSelect',
  props: {
    // InputNumber 相关 props
    value: {
      type: Number,
      default: undefined,
    },
    min: {
      type: Number,
      default: undefined,
    },
    max: {
      type: Number,
      default: undefined,
    },
    step: {
      type: Number,
      default: 1,
    },
    placeholder: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    precision: {
      type: Number,
      default: undefined,
    },

    // Select 相关 props
    selectValue: {
      type: [String, Number],
      default: undefined,
    },
    selectOptions: {
      type: Array,
      default: () => [],
      validator: (value) => {
        return value.every(item =>
          typeof item === 'object'
          && Object.prototype.hasOwnProperty.call(item, 'value')
          && Object.prototype.hasOwnProperty.call(item, 'label'),
        )
      },
    },
    selectPlaceholder: {
      type: String,
      default: '请选择',
    },
    selectDisabled: {
      type: Boolean,
      default: false,
    },

    // 样式相关 props
    inputWidth: {
      type: [String, Number],
      default: 120,
    },
    selectWidth: {
      type: [String, Number],
      default: 80,
    },

    // 是否支持前缀 select
    enableAddonBefore: {
      type: Boolean,
      default: false,
    },
    beforeSelectValue: {
      type: [String, Number],
      default: undefined,
    },
    beforeSelectOptions: {
      type: Array,
      default: () => [],
    },
    beforeSelectWidth: {
      type: [String, Number],
      default: 80,
    },

    // 回调函数 props
    onNumberChange: {
      type: Function,
      default: null,
    },
    onSelectChange: {
      type: Function,
      default: null,
    },
    onBeforeSelectChange: {
      type: Function,
      default: null,
    },
  },

  computed: {
    inputStyle() {
      return {
        width: typeof this.inputWidth === 'number' ? `${this.inputWidth}px` : this.inputWidth,
      }
    },
    selectStyle() {
      return {
        width: typeof this.selectWidth === 'number' ? `${this.selectWidth}px` : this.selectWidth,
      }
    },
    beforeSelectStyle() {
      return {
        width: typeof this.beforeSelectWidth === 'number' ? `${this.beforeSelectWidth}px` : this.beforeSelectWidth,
      }
    },
  },

  methods: {
    handleNumberChange(value) {
      // 优先调用 props 传递的回调函数
      if (this.onNumberChange) {
        this.onNumberChange(value)
      }
      // 保留 $emit 以保证兼容性
      this.$emit('change', value)
      this.$emit('numberChange', value)
    },

    handleSelectChange(value) {
      // 优先调用 props 传递的回调函数
      if (this.onSelectChange) {
        this.onSelectChange(value)
      }
      // 保留 $emit 以保证兼容性
      this.$emit('selectChange', value)
    },

    handleBeforeSelectChange(value) {
      // 优先调用 props 传递的回调函数
      if (this.onBeforeSelectChange) {
        this.onBeforeSelectChange(value)
      }
      // 保留 $emit 以保证兼容性
      this.$emit('beforeSelectChange', value)
    },

    handleFocus(e) {
      this.$emit('focus', e)
    },

    handleBlur(e) {
      this.$emit('blur', e)
    },
  },
}
</script>

<template>
  <div class="input-number-with-select">
    <!-- 前缀 select -->
    <a-select
      v-if="enableAddonBefore"
      :value="beforeSelectValue"
      :style="beforeSelectStyle"
      :placeholder="selectPlaceholder"
      :disabled="selectDisabled"
      class="addon-before"
      @change="handleBeforeSelectChange"
    >
      <a-select-option
        v-for="option in beforeSelectOptions"
        :key="option.value"
        :value="option.value"
      >
        {{ option.label }}
      </a-select-option>
    </a-select>

    <!-- InputNumber -->
    <a-input-number
      :value="value"
      :min="min"
      :max="max"
      :step="step"
      :placeholder="placeholder"
      :disabled="disabled"
      :precision="precision"
      :style="inputStyle"
      class="input-number"
      @change="handleNumberChange"
      @focus="handleFocus"
      @blur="handleBlur"
    />

    <!-- 后缀 select -->
    <a-select
      :value="selectValue"
      :style="selectStyle"
      :placeholder="selectPlaceholder"
      :disabled="selectDisabled"
      class="addon-after"
      @change="handleSelectChange"
    >
      <a-select-option
        v-for="option in selectOptions"
        :key="option.value"
        :value="option.value"
      >
        {{ option.label }}
      </a-select-option>
    </a-select>
  </div>
</template>

<style lang="scss" scoped>
.input-number-with-select {
  display: inline-flex;
  align-items: center;
}

.addon-before {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;

  :deep(.ant-select-selector) {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-right: none !important;
  }
}

.input-number {
  // 左上左下角不变
  border-top-left-radius: 4px !important;
  border-bottom-left-radius: 4px !important;
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;

  &:not(:first-child) {
    border-left: none !important;
  }

  &:not(:last-child) {
    border-right: none !important;
  }

  :deep(.ant-input-number) {
    border-radius: 0 !important;

    &:not(:first-child) {
      border-left: none !important;
    }

    &:not(:last-child) {
      border-right: none !important;
    }
  }

  // 确保组件在 focus 状态下的边框正确显示
  :deep(.ant-input-number-focused) {
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    border-color: #40a9ff;
    z-index: 1;
    position: relative;
  }
}

.addon-after {
  /* border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important; */

  :deep(.ant-select-selector) {
    background-color: #F7F8FA !important;
    border-top-left-radius: 0 !important;
    border-bottom-left-radius: 0 !important;
    border-left: none !important;
  }

  // 确保背景色样式生效
  :deep(.ant-select) {
    .ant-select-selector {
      background-color: #F7F8FA !important;
    }
  }

  :deep() {
    .ant-select-selector,
    .ant-select-selection,
    .ant-select-selection__rendered {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
      border-top-right-radius: 4px !important;
      border-bottom-right-radius: 4px !important;
      background-color: #F7F8FA !important;
    }
  }
}
</style>
