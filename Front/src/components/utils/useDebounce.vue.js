import { ref, watch } from 'vue'

// Vue 版本的 useDebounce hook
export default function useDebounce(value, delay) {
  const debouncedValue = ref(value)
  let timeoutId = null

  watch(
    () => value,
    (newValue) => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      timeoutId = setTimeout(() => {
        debouncedValue.value = newValue
      }, delay)
    },
    { immediate: true },
  )

  return debouncedValue
}
