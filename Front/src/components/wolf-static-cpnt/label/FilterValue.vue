<script>
import _ from 'lodash'
import moment from 'moment'

export default {
  name: 'LabelFilterValue',
  inject: ['filterContext'],
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      items: [],
      fieldValue: null,
      fieldShowValue: null,
      menuVisible: false,
      checked: false,
      dateRangeValue: null,
    }
  },
  computed: {
    context() {
      return this.filterContext()
    },
    dataProvider() {
      return this.context.dataProvider
    },
    currentComponent() {
      const { fieldType, operator } = this.value

      switch (operator) {
        case 'EQ':
        case 'NE':
        case 'GT':
        case 'GTE':
        case 'LT':
        case 'LTE':
          if (fieldType === 'DATE' || fieldType === 'DATETIME' || fieldType === 'TIMESTAMP'
            || fieldType === 'HIVE_DATE' || fieldType === 'HIVE_TIMESTAMP') {
            return 'DateInput'
          }
          return 'TextInput'
        case 'BETWEEN':
          if (fieldType === 'DATE' || fieldType === 'DATETIME' || fieldType === 'TIMESTAMP'
            || fieldType === 'HIVE_DATE' || fieldType === 'HIVE_TIMESTAMP') {
            return 'DateBetweenInput'
          }
          return 'NumberBetweenInput'
        case 'IN':
        case 'NOT_IN':
        case 'LIKE':
        case 'NOT_LIKE':
        case 'START_WITH':
        case 'NOT_START_WITH':
        case 'END_WITH':
        case 'NOT_END_WITH':
          return 'EnumInput'
        case 'IS_NOT_NULL':
        case 'IS_NULL':
        case 'IS_TRUE':
        case 'IS_FALSE':
        case 'ALL':
          return 'EmptySpan'
        default:
          return 'DefaultInput'
      }
    },

    // 文本输入相关计算属性
    filteredItems() {
      const items = _.isArray(this.items) ? this.items.filter(n => !!n.value) : []
      return items.map(v => ({
        value: v.priorityShow === 2 ? (v.displayValue ? v.valueAndDisplayValue : v.value) : v.value,
        text: v.priorityShow === 2 ? (v.displayValue ? v.valueAndDisplayValue : v.value) : v.value,
      }))
    },

    // 日期输入相关计算属性
    showTime() {
      const { fieldType } = this.value
      return (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP')
        ? { format: 'HH:mm:ss' }
        : null
    },

    format() {
      const { fieldType } = this.value
      return (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP')
        ? 'YYYY-MM-DD HH:mm:ss'
        : 'YYYY-MM-DD'
    },

    unit() {
      const { fieldType } = this.value
      return (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP')
        ? 'second'
        : 'day'
    },

    dateValue() {
      return this.fieldValue ? moment(this.fieldValue) : null
    },

    // 数字范围输入相关计算属性
    normalizedFieldValue() {
      return _.isArray(this.fieldValue) ? this.fieldValue : []
    },

    // 枚举输入相关计算属性
    normalizedFieldShowValue() {
      return _.isArray(this.fieldShowValue) ? this.fieldShowValue : []
    },

    filteredEnumItems() {
      return this.items.filter(n => !!n.value).map(item => ({
        id: item.id,
        value: item.priorityShow === 2 && item.displayValue ? item.valueAndDisplayValue : item.value,
        label: item.priorityShow === 2 && item.displayValue ? item.valueAndDisplayValue : (item.value ? item.value : item),
      }))
    },
  },
  watch: {
    'value.value': {
      handler(newVal) {
        this.fieldValue = newVal
        // 更新日期范围值
        if (this.currentComponent === 'DateBetweenInput') {
          this.dateRangeValue = this.longToMoment(newVal)
        }
      },
      immediate: true,
    },

    'value.showValue': {
      handler(newVal) {
        this.fieldShowValue = newVal
      },
      immediate: true,
    },

    'value.id': {
      async handler() {
        // 当标签ID变化时，获取标签值
        // if (this.value.tagInfo && this.value.tagInfo.userLabelValues) {
        //   this.items = this.value.tagInfo.userLabelValues;
        // }
        // fixme 这里切换标签调用接口
        if (this.value.tagInfo) {
          const tagValues = await this.dataProvider.getTagValuesById(this.value.label)
          this.items = tagValues
        }
      },
      immediate: true,
    },
  },
  methods: {
    onChangeFieldValue(v) {
      this.value.value = v
      this.value.showValue = v
      this.fieldValue = v
      this.fieldShowValue = this.value.showValue
      this.onChange(v)
    },

    setMenuVisible(visible) {
      this.menuVisible = visible
    },

    // 文本输入方法
    // filterOption(inputValue, option) {
    //   return `${option.value}`.toUpperCase().indexOf(typeof inputValue === 'string' ? inputValue.toUpperCase() : '') !== -1;
    // },
    filterOption(input, option) {
      return (
        option.componentOptions.children[0].text.toUpperCase().indexOf(input.toUpperCase()) >= 0
      )
    },

    // 日期输入方法
    handleDateChange(date) {
      this.onChangeFieldValue(date ? date.startOf(this.unit).valueOf() : null)
    },

    // 日期范围输入方法
    longToMoment(fv) {
      return _.isArray(fv) ? fv.map(v => moment(v)) : undefined
    },

    handleDateRangeChange(m) {
      this.dateRangeValue = m
      this.onChangeFieldValue(m && m[0] && m[1] && [m[0].startOf(this.unit).valueOf(), m[1].startOf(this.unit).valueOf()])
    },

    // 数字范围输入方法
    handleMinChange(value) {
      if (!isNaN(value)) {
        this.onChangeFieldValue([value, this.normalizedFieldValue[1]])
      }
    },

    handleMaxChange(value) {
      if (!isNaN(value)) {
        this.onChangeFieldValue([this.normalizedFieldValue[0], value])
      }
    },

    // 枚举输入方法
    changeSelect(v) {
      if (this.checked) {
        const mapArr = [',', '，', ' ', '、', ';', '；']
        const regex = new RegExp(`[${mapArr.join('')}]`, 'g')

        let result = v.map(item => item.replace(regex, ',').split(','))
          .reduce((acc, curr) => acc.concat(curr), [])

        result = result.filter(item => item)
        result = _.uniq(result)
        this.onChangeFieldValue(result)
      }
      else {
        this.onChangeFieldValue(v)
      }
    },

    changeCheckBox(e) {
      if (!e.target.checked) {
        this.onChangeFieldValue([])
      }
      this.checked = e.target.checked
    },

    // 默认输入方法
    handleStringChange(e) {
      this.onChangeFieldValue(e.target.value)
    },
  },
}
</script>

<template>
  <div>
    <!-- 文本输入 -->
    <a-auto-complete
      v-if="currentComponent === 'TextInput'"
      style="width: 170px"
      :data-source="filteredItems"
      :show-search="items.length > 0"
      :value="fieldShowValue"
      :filter-option="filterOption"
      @change="onChangeFieldValue"
      @dropdownVisibleChange="setMenuVisible"
    />

    <!-- 日期输入 -->
    <a-date-picker
      v-else-if="currentComponent === 'DateInput'"
      placeholder="请选择日期"
      :show-time="showTime"
      :format="format"
      :allow-clear="false"
      :value="dateValue"
      @change="handleDateChange"
    />

    <!-- 日期范围输入 -->
    <a-range-picker
      v-else-if="currentComponent === 'DateBetweenInput'"
      :allow-clear="false"
      :show-time="showTime"
      :format="format"
      :placeholder="['开始日期', '结束日期']"
      :value="dateRangeValue"
      @change="handleDateRangeChange"
    />

    <!-- 数字范围输入 -->
    <div v-else-if="currentComponent === 'NumberBetweenInput'">
      <a-input-number
        style="width: 100px; margin-right: 5px"
        placeholder="最小值"
        :value="normalizedFieldValue[0]"
        @change="handleMinChange"
      />
      至
      <a-input-number
        style="width: 100px; margin-left: 5px"
        placeholder="最大值"
        :value="normalizedFieldValue[1]"
        @change="handleMaxChange"
      />
    </div>

    <!-- 枚举输入 -->
    <div v-else-if="currentComponent === 'EnumInput'" style="display: flex; gap: 12px">
      <a-select
        mode="tags"
        allow-clear
        :max-tag-count="10"
        :value="normalizedFieldShowValue"
        style="min-width: 150px; max-width: 380px"
        option-label-prop="label"
        @change="changeSelect"
        @dropdownVisibleChange="setMenuVisible"
      >
        <a-select-option
          v-for="item in filteredEnumItems"
          :key="item.id"
          :value="item.value"
          :label="item.label"
        >
          {{ item.label }}
        </a-select-option>
      </a-select>
      <a-checkbox
        :checked="checked"
        style="height: 30px; white-space: nowrap; display: flex; align-items: center"
        @change="changeCheckBox"
      >
        <a-tooltip
          :overlay-style="{ maxWidth: '350px' }"
          title="智能分割：自动识别逗号、分号等分隔符，将输入内容分割为多个标签"
        >
          智能分割
        </a-tooltip>
      </a-checkbox>
    </div>

    <!-- 空组件 -->
    <span v-else-if="currentComponent === 'EmptySpan'" />

    <!-- 默认输入 -->
    <a-input
      v-else
      placeholder="请输入值"
      :disabled="!value.operator"
      :value="fieldValue"
      @change="handleStringChange"
    />
  </div>
</template>

<style scoped>
/* 样式将从label.scss中继承 */
</style>
