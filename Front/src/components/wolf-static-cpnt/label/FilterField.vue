<script>
import { Dropdown, Input, Menu, Spin, Tag, Tooltip, Tree } from 'ant-design-vue'
import _ from 'lodash'
import moment from 'moment'

// 工具函数
function findObj(data, key) {
  let info = {}
  data.forEach((n) => {
    if (n.key === key) {
      info = n
    }
    if (n.children) {
      n.children.forEach((w) => {
        if (w.key === key) {
          info = w
        }
        if (w.children) {
          w.children.forEach((h) => {
            if (h.key === key) {
              info = h
            }
          })
        }
      })
    }
  })
  return info
}

function getLevelList(_list, _categoryList) {
  const _treeData = []
  let _expandedKeys = []
  const categoryObj = {}
  _categoryList.forEach((n) => {
    categoryObj[`key.${n.id}`] = n
  })

  _list.forEach((n) => {
    const info = categoryObj[`key.${n.categoryId}`]
    if (!info) {
      _treeData.push({
        title: n.displayName,
        key: `${n.id}`,
        isLeaf: true,
      })
    }
    else {
      const codeArr = `${info.path}${info.id}`
        .split(',')
        .filter(x => x !== '0')
        .map(h => `key.${h}`)
      _expandedKeys = _expandedKeys.concat(codeArr)

      if (codeArr[0] && !_treeData.find(item => item.key === codeArr[0])) {
        _treeData.push({
          title: categoryObj[codeArr[0]]?.name || '未知分类',
          key: codeArr[0],
          isLeaf: false,
        })
      }

      if (codeArr[1]) {
        const category1Info = findObj(_treeData, codeArr[0])
        if (!category1Info.children) {
          category1Info.children = [
            {
              title: categoryObj[codeArr[1]]?.name || '未知分类',
              key: codeArr[1],
              isLeaf: false,
            },
          ]
        }
        else if (category1Info.children.findIndex(w => w.key === codeArr[1]) === -1) {
          category1Info.children.push({
            title: categoryObj[codeArr[1]]?.name || '未知分类',
            key: codeArr[1],
            isLeaf: false,
          })
        }
      }
      if (codeArr[2]) {
        const category2Info = findObj(_treeData, codeArr[1])
        if (!category2Info.children) {
          category2Info.children = [
            {
              title: categoryObj[codeArr[2]]?.name || '未知分类',
              key: codeArr[2],
              isLeaf: false,
            },
          ]
        }
        else if (category2Info.children.findIndex(w => w.key === codeArr[2]) === -1) {
          category2Info.children.push({
            title: categoryObj[codeArr[2]]?.name || '未知分类',
            key: codeArr[2],
            isLeaf: false,
          })
        }
      }

      const categoryInfo = findObj(_treeData, `key.${n.categoryId}`)
      if (!categoryInfo.children) {
        categoryInfo.children = [{ title: n.displayName, key: `${n.id}`, isLeaf: true }]
      }
      else {
        categoryInfo.children.push({
          title: n.displayName,
          key: `${n.id}`,
          isLeaf: true,
        })
      }
    }
  })

  _treeData.sort((a, b) => (a.isLeaf ? 1 : 0) - (b.isLeaf ? 1 : 0))
  return { _treeData, _expandedKeys }
}

function getObjData(_list) {
  const data = {}
  _list.forEach((n) => {
    data[n.id] = n
  })
  return data
}

function updateTreeData(list, key, children) {
  return list.map((node) => {
    if (node.key === key) {
      return { ...node, children }
    }
    else if (node.children) {
      return {
        ...node,
        children: updateTreeData(node.children, key, children),
      }
    }
    return node
  })
}

export default {
  name: 'LabelFilterField',
  components: {
    'a-dropdown': Dropdown,
    'a-menu': Menu,
    'a-input': Input,
    'a-tree': Tree,
    'a-tag': Tag,
    'a-spin': Spin,
    'a-tooltip': Tooltip,
  },
  inject: ['filterContext'],
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      treeData: [],
      visible: false,
      tagInfo: {},
      searchValue: this.value.label || '',
      displayName: '',
      selectedKeys: this.value.id ? [this.value.id] : [],
      objData: {},
      selectedTag: this.value?.displayName || '请选择',
      loadedKeys: [],
      expandedKeys: [],
      flag: true,
      loading: false,
      isOnFocus: false,
    }
  },
  computed: {
    context() {
      return this.filterContext()
    },
    dataProvider() {
      return this.context.dataProvider
    },
  },
  watch: {
    value: {
      handler(newValue) {
        this.searchValue = newValue.displayName || ''
        this.selectedKeys = newValue.id ? [newValue.id] : []
        this.selectedTag = newValue?.displayName || '请选择'
      },
      deep: true,
    },
    displayName: {
      handler() {
        this.init()
      },
    },
  },
  methods: {
    async init() {
      this.loading = true
      try {
        if (this.displayName) {
          const _list = await this.dataProvider.getTagList({ displayName: this.displayName })
          const _categoryList = await this.dataProvider.findCategoryByProjectId()
          // 确保数据有效
          const validList = Array.isArray(_list) ? _list : []
          const validCategoryList = Array.isArray(_categoryList) ? _categoryList : []

          const result = getLevelList(validList, validCategoryList)
          this.loadedKeys = []
          this.treeData = result._treeData || []
          this.expandedKeys = result._expandedKeys || []
          const data = getObjData(validList)
          this.objData = { ...this.objData, ...data }
          this.flag = true
        }
        else if (this.flag) {
          this.flag = false
          const data = await this.getAsyncData('key.0')
          const _list = await this.dataProvider.getTagList({
            displayName: this.value.displayName,
          })
          let obj = {}
          if (_list && Array.isArray(_list)) {
            obj = _list.find(n => Number(n.id) === Number(this.value.id))
            if (obj) {
              this.value.changeProperty({ ...this.value, tagInfo: obj })
              this.onChange(this.value)
            }
          }
          this.loadedKeys = []
          this.expandedKeys = []
          this.treeData = Array.isArray(data) ? data : []
        }
      }
      catch (error) {
        console.error('Failed to initialize:', error)
        this.treeData = []
        this.expandedKeys = []
        this.loadedKeys = []
      }

      setTimeout(() => {
        this.loading = false
      }, 1000)
    },

    async getAsyncData(key) {
      try {
        const result = await this.dataProvider.findAllCategory({
          categoryId: Number.parseInt(key.split('.')[1]),
        })
        const data = []
        const obj = {}

        if (result && result.categoryList && Array.isArray(result.categoryList)) {
          result.categoryList.forEach((n) => {
            if (n && n.name && n.id) {
              data.push({
                title: n.name,
                key: `key.${n.id}`,
                isLeaf: false,
              })
            }
          })
        }

        if (result && result.userLabels && Array.isArray(result.userLabels)) {
          result.userLabels.forEach((n) => {
            if (n && n.displayName && n.id) {
              data.push({
                title: n.displayName,
                key: `${n.id}`,
                isLeaf: true,
              })
              obj[`${n.id}`] = n
            }
          })
        }

        this.objData = { ...this.objData, ...obj }
        return data
      }
      catch (error) {
        console.error('Failed to get async data:', error)
        return []
      }
    },

    async onLoadData(treeNode) {
      const eventKey = treeNode.eventKey
      const children = treeNode.dataRef.children
      if (children) {
        return
      }
      const data = await this.getAsyncData(eventKey)
      this.treeData = updateTreeData(this.treeData, eventKey, data)
    },

    onSelect(selectedKeys, info) {
      if (info.node.isLeaf) {
        if (this.isEmptyObject(this.tagInfo) || Number(selectedKeys[0]) !== Number(this.tagInfo.id)) {
          return
        }
        this.selectedKeys = selectedKeys
        this.handleVisibleChange(false, selectedKeys)

        const selectedData = this.objData[selectedKeys[0]]
        if (selectedData) {
          const label = selectedData.name
          const labelDisplayName = selectedData.displayName
          const fieldType = selectedData.dataType

          this.value.changeProperty({
            id: selectedKeys[0],
            displayName: labelDisplayName,
            label,
            fieldType: fieldType || 'STRING',
            tagInfo: this.tagInfo,
          })
          this.value.changeDateType(this.tagInfo.busiDate ? 'LATEST' : 'RELATIVE', true)
          this.onChange(this.value)
        }
      }
    },

    async handleNodeMouseEnter(key, isLeaf) {
      if (isLeaf) {
        const _tagInfo = this.objData[key]
        if (_tagInfo) {
          const _items = await this.dataProvider.getTagValuesById(_tagInfo.id)
          let rule = ''
          const env = localStorage.getItem('env')
          if (env === 'NS' && this.dataProvider?.getEffectRule) {
            rule = await this.dataProvider.getEffectRule({
              ruleType: 0,
              labelId: _tagInfo.id,
            })
          }
          const param = { ..._tagInfo, userLabelValues: _items, rule }
          this.tagInfo = param
        }
      }
    },

    onLoad(loadedKeys) {
      this.loadedKeys = loadedKeys
    },

    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys
    },

    handleVisibleChange(flag, data) {
      if (!flag) {
        const lastData = (data && data[0]) || this.selectedKeys[0] || ''
        const _value = (lastData && this.objData[lastData]?.displayName) || this.value.displayName
        this.searchValue = _value || ''
        this.displayName = ''
        if (_value) {
          this.selectedTag = _value
        }
        if (this.$refs.searchRef) {
          this.$refs.searchRef.blur()
        }
      }
      this.visible = flag
    },

    onSearch: _.debounce(function (e) {
      const _value = e.target.value
      this.searchValue = _value
      this.displayName = _value
    }, 1000),

    onFocus() {
      this.searchValue = ''
      this.displayName = ''
      this.isOnFocus = true
    },

    onBlur() {
      this.isOnFocus = false
    },

    renderTagValue(info, type) {
      if (!info || !info.value) {
        return ''
      }

      let showTagValue
        = info.value.length <= 10
          ? info.priorityShow === 2 && info.displayValue
            ? info.valueAndDisplayValue || info.value
            : info.value
          : info.priorityShow === 2 && info.displayValue
            ? `${info.valueAndDisplayValue || info.value}`
            : `${info.value}`

      if (this.tagInfo.dataType === 'TIMESTAMP') {
        try {
          showTagValue = moment(Number.parseInt(showTagValue)).format('YYYY-MM-DD HH:mm:ss')
        }
        catch (e) {
          console.warn('Invalid timestamp:', showTagValue)
        }
      }

      if (type === 'title') {
        return showTagValue
      }
      else {
        return showTagValue.length > 10 ? `${showTagValue.substring(0, 10)}...` : showTagValue
      }
    },

    // 辅助方法：用于模板中判断 lodash isEmpty
    isEmptyObject(obj) {
      return _.isEmpty(obj)
    },

    isEmptyRule(rule) {
      return _.isEmpty(rule)
    },
  },
  mounted() {
    this.init()
  },
}
</script>

<template>
  <div>
    <a-dropdown
      :trigger="['click']"
      :visible="visible"
      :get-popup-container="(triggerNode) => triggerNode.parentNode"
      @visibleChange="handleVisibleChange"
    >
      <div class="clickWrapper">
        <a-input
          ref="searchRef"
          class="ant-dropdown-link"
          :placeholder="selectedTag"
          :value="searchValue"
          :style="{ paddingRight: '25px' }"
          :disabled="loading"
          @change="onSearch"
          @focus="onFocus"
          @blur="onBlur"
        />
        <a-icon
          type="down"
          :style="{
            position: 'absolute',
            right: '8px',
            top: '50%',
            transform: 'translateY(-50%)',
            color: 'rgba(0,0,0,.45)',
            fontSize: '12px',
            pointerEvents: 'none',
          }"
        />
      </div>
      <template #overlay>
        <div>
          <div
            class="cpnt-label"
            :style="{
              display: 'flex',
              padding: '10px 0px',
              width: '640px',
              minHeight: '320px',
            }"
          >
            <!-- 左侧树形结构 -->
            <div
              :style="{
                width: '320px',
                borderRight: '1px solid #E1E1E1',
                maxHeight: '320px',
                overflow: 'auto',
              }"
              class="labelTree"
            >
              <a-spin :spinning="loading">
                <a-tree
                  :show-icon="true"
                  :tree-data="treeData"
                  :selected-keys="selectedKeys"
                  :loaded-keys="loadedKeys"
                  :expanded-keys="expandedKeys"
                  :load-data="onLoadData"
                  :height="300"
                  @select="onSelect"
                  @load="onLoad"
                  @expand="onExpand"
                >
                  <template #title="{ title, key, isLeaf }">
                    <span @mouseenter="handleNodeMouseEnter(key, isLeaf)">{{ title }}</span>
                  </template>
                </a-tree>
              </a-spin>
            </div>

            <!-- 右侧详细信息 -->
            <div
              :style="{
                width: '320px',
                maxHeight: '320px',
                overflowY: 'auto',
                padding: '3px 10px',
              }"
            >
              <div
                :style="{
                  fontFamily: 'PingFangSC-Regular',
                  fontSize: '16px',
                  fontWeight: 600,
                  color: '#000000',
                }"
              >
                {{ tagInfo.displayName }}
              </div>
              <div
                :style="{
                  fontFamily: 'PingFangSC-Regular',
                  fontSize: '14px',
                  color: 'rgba(0, 0, 0, 0.65)',
                  marginTop: '8px',
                }"
              >
                {{ tagInfo.name }}
              </div>
              <div
                :style="{
                  fontFamily: 'PingFangSC-Regular',
                  fontSize: '14px',
                  color: 'rgba(0, 0, 0, 0.65)',
                  marginTop: '8px',
                }"
              >
                {{ tagInfo.dataType }}
              </div>

              <div v-if="!isEmptyRule(tagInfo.rule)">
                标签业务规则:
                <pre :style="{ color: 'rgba(0, 0, 0, 0.65)' }">{{ tagInfo.rule }}</pre>
              </div>

              <div
                class="cpnt-label-remark"
                :style="{
                  opacity: 0.5,
                  fontFamily: 'PingFangSC-Regular',
                  fontSize: '14px',
                  color: 'rgba(0,0,0,0.65)',
                  margin: '0 auto',
                  paddingTop: '16px',
                }"
                :title="tagInfo.remark"
              >
                {{ tagInfo.remark }}
              </div>
              <div
                :style="{
                  marginTop: '16px',
                  fontFamily: 'PingFangSC-Regular',
                  fontSize: '14px',
                  color: 'rgba(0,0,0,0.65)',
                }"
              >
                标签值
              </div>
              <div>
                <template v-if="tagInfo.userLabelValues">
                  <a-tooltip
                    v-for="(item, index) in tagInfo.userLabelValues.slice(0, 10)"
                    :key="item.id || index"
                    :title="renderTagValue(item, 'title')"
                  >
                    <a-tag style="margin-bottom: 4px">
                      {{ renderTagValue(item) }}
                    </a-tag>
                  </a-tooltip>
                  <span v-if="tagInfo.userLabelValues.length > 10">...</span>
                </template>
              </div>
            </div>
          </div>
        </div>
      </template>
    </a-dropdown>
  </div>
</template>

<style scoped>
.clickWrapper {
  position: relative;
  display: inline-block;
  width: 100%;
}

.cpnt-label {
  background: white;
}

.labelTree {
  background: #fafafa;
}

.fieldDirectoryTree {
  background: transparent;
}

.cpnt-label-remark {
  word-break: break-all;
}

/* 修复 Menu 组件样式 */
.filter-field-menu {
  padding: 0;
  border: none;
  box-shadow: none;
}

.filter-field-menu .ant-menu-item {
  padding: 0;
  margin: 0;
  height: auto;
  line-height: normal;
}

.filter-field-menu .ant-menu-item:hover {
  background: transparent;
}

.filter-field-menu .ant-menu-item-selected {
  background: transparent;
}
</style>
