<script>
import { Dropdown, Input, Menu } from 'ant-design-vue'
import FilterConfig from './FilterConfig'

const { DATE_TYPE_MAP, DATE_TYPE_NOLATEST_MAP } = FilterConfig

export default {
  name: 'LabelFilterTimeType',
  components: {
    'a-dropdown': Dropdown,
    'a-menu': Menu,
    'a-menu-item': Menu.Item,
    'a-input': Input,
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      searchText: DATE_TYPE_MAP[this.value.dateType] || 'RELATIVE',
      menuVisible: false,
    }
  },
  computed: {
    dateTypeMap() {
      return this.value?.tagInfo?.busiDate ? DATE_TYPE_MAP : DATE_TYPE_NOLATEST_MAP
    },
    filteredOptions() {
      if (!this.value?.id)
        return []

      return Object.entries(this.dateTypeMap)
        // .filter(([key, value]) => !this.searchText || value.indexOf(this.searchText) >= 0)
        .map(([key, value]) => ({ key, value }))
    },
  },
  watch: {
    'value.dateType': {
      handler(newDateType) {
        if (this.menuVisible) {
          this.searchText = ''
        }
        else if (DATE_TYPE_MAP[newDateType]) {
          this.searchText = DATE_TYPE_MAP[newDateType]
        }
      },
    },
    'menuVisible': {
      handler(visible) {
        if (visible) {
          this.searchText = ''
        }
        else if (DATE_TYPE_MAP[this.value.dateType]) {
          this.searchText = DATE_TYPE_MAP[this.value.dateType]
        }
      },
    },
  },
  mounted() {
    this.searchText = DATE_TYPE_MAP[this.value.dateType] || ''
  },
  methods: {
    onSelect(type) {
      const resetTimes = type !== this.value.dateType
      this.value.changeDateType(type, resetTimes)
      this.value.changeRule('checkUserTag', false)
      this.onChange(this.value)
      this.searchText = DATE_TYPE_MAP[this.value.dateType]
    },

    onMenuVisible(visible) {
      if (visible) {
        this.searchText = ''
      }
      this.menuVisible = visible
    },

    handleSearchChange(e) {
      this.searchText = e.target.value
    },

    handleFocus(event) {
      event.target.select()
    },
  },
}
</script>

<template>
  <a-dropdown
    :get-popup-container="(triggerNode) => triggerNode.parentNode"
    :trigger="['click']"
    @visible-change="onMenuVisible"
  >
    <div class="clickWrapper">
      <a-input
        v-model="searchText"
        class="ant-dropdown-link"
        placeholder="时间类型"
        @change="handleSearchChange"
        @focus="handleFocus"
      />
      <a-icon type="down" :style="{ color: 'rgba(0,0,0,.45)', fontSize: '12px' }" />
    </div>
    <template #overlay>
      <a-menu
        force-sub-menu-render
        :style="{
          maxHeight: '400px',
          overflowY: 'auto',
          maxWidth: '500px',
          overflowX: 'auto',
        }"
      >
        <a-menu-item
          v-for="(item, index) in filteredOptions"
          :key="index"
          @click="() => onSelect(item.key)"
        >
          {{ item.value }}
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<style scoped>
.clickWrapper {
  position: relative;
  display: inline-block;
  width: 100%;
}

.clickWrapper .anticon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}
</style>
