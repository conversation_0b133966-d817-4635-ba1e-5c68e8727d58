<script>
import { Select } from 'ant-design-vue'
import FilterConfig from './FilterConfig'

export default {
  name: 'LabelFilterOperator',
  components: {
    'a-select': Select,
    'a-select-option': Select.Option,
  },
  inject: ['filterContext'],
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  computed: {
    context() {
      return this.filterContext()
    },
    isUserGroup() {
      return this.context.isUserGroup
    },
    operatorOptions() {
      const operatorList = this.isUserGroup
        ? FilterConfig.userGroupOperatorList
        : FilterConfig.operatorList

      if (!this.value.fieldType)
        return operatorList

      const typeOperator = this.isUserGroup
        ? FilterConfig.userGroupTypeOperator
        : FilterConfig.typeOperator

      const allowedOperators = typeOperator[this.value.fieldType] || []

      return operatorList.filter(item => allowedOperators.includes(item.operator))
    },
  },
  methods: {
    handleChange(operator) {
      this.value.changeOperator(operator)
      this.onChange(this.value)
    },
  },
}
</script>

<template>
  <a-select
    style="width: 100%"
    placeholder="操作符"
    :value="value.operator"
    @change="handleChange"
  >
    <a-select-option
      v-for="item in operatorOptions"
      :key="item.operator"
      :value="item.operator"
    >
      {{ item.name }}
    </a-select-option>
  </a-select>
</template>

<style scoped>
/* 样式将从label.scss中继承 */
</style>
