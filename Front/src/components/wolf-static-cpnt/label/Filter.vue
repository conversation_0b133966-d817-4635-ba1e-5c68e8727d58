<script>
import { Button } from 'ant-design-vue'
import _ from 'lodash'
import Log from '../../utils/log'
import FilterConfig from './FilterConfig'
import { FilterContextProvider } from './FilterContext'
import FilterListGroup from './FilterListGroup.vue'
import FilterModelUtil from './FilterModelUtil'

const log = Log.getLogger('Label')

export default {
  name: 'CPNTLabel',
  components: {
    'a-button': Button,
    FilterListGroup,
    FilterContextProvider,
  },
  props: {
    dataProvider: {
      type: Object,
      default: () => ({}),
    },
    value: {
      type: Object,
      default: () => ({}),
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    mode: {
      type: String,
      default: 'edit',
    },
    showInitLine: {
      type: Boolean,
      default: true,
    },
    checked: {
      type: Boolean,
      default: false,
    },
    campaignInfo: {
      type: Object,
      default: () => ({}),
    },
    isUserGroup: {
      type: Boolean,
      default: false,
    },
    isCampaignV2: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const { maxFilterCount } = FilterConfig
    return {
      maxFilterCount,
      currentValue: this.initValue(),
      propsValue: {},
    }
  },
  computed: {
    context() {
      let filterCount = 0
      if (this.currentValue && this.currentValue.filters) {
        filterCount = this.currentValue.filters.map(v => v.filters?.length || 0).reduce((a, b) => a + b, 0)
      }

      return {
        dataProvider: this.dataProvider,
        logProvider: Log,
        canAdd: filterCount < this.maxFilterCount,
        mode: this.mode || 'edit',
        filterCount,
        validating: false,
        showInitLine: this.showInitLine,
        checked: this.checked,
        campaignInfo: this.campaignInfo,
        isUserGroup: this.isUserGroup,
        isCampaignV2: this.isCampaignV2,
      }
    },
  },

  watch: {
    value: {
      handler(newValue) {
        log.debug('props.value changed', JSON.stringify(newValue))
        if (!_.isEqual(newValue, this.propsValue)) {
          this.currentValue = FilterModelUtil.fromJson(newValue)
        }
      },
      deep: true,
    },
  },
  mounted() {
    log.debug('Before Render', JSON.stringify(this.currentValue), this.context.canAdd)
  },

  methods: {
    initValue() {
      return this.value && this.value.filters && this.value.filters.length > 0
        ? FilterModelUtil.fromJson(this.value)
        : FilterModelUtil.initCreateFilterGroup(this.showInitLine)
    },

    /**
     * 添加过滤组
     */
    addFilterGroup() {
      FilterModelUtil.addFilterGroupWithOneFilter(this.currentValue)
      this.onValueChange(this.currentValue)
    },

    /**
     * 当过滤组修改时回调
     * @param {object} v 过滤组
     */
    onValueChange(v) {
      if (!v.connector) {
        v.connector = 'AND'
      }
      log.debug('onValueChanged', JSON.stringify(v))
      const _v = FilterModelUtil.getValidJson(v)
      this.propsValue = _v
      this.onChange && this.onChange(_v, v)
      this.currentValue = { ...v }
    },

    /**
     * 验证方法
     */
    isValid(flag) {
      // 设置验证状态
      return FilterModelUtil.isFilterListGroupValid(this.currentValue, flag)
    },

    /**
     * 获取过滤器数量
     */
    getFilterCount() {
      if (this.currentValue && this.currentValue.filters) {
        return this.currentValue.filters.map(v => v.filters?.length || 0).reduce((a, b) => a + b, 0)
      }
      return 0
    },
  },
}
</script>

<template>
  <FilterContextProvider :value="context">
    <div class="wolf-static-component_filter_FilterGroupPanel_label">
      <FilterListGroup :value="currentValue" :on-change="onValueChange" />
      <div class="FilterAdder">
        <a-button
          type="dashed"
          :disabled="!context.canAdd"
          :style="{
            display: context.mode === 'detail' ? 'none' : 'inline-block',
          }"
          @click="addFilterGroup"
        >
          <a-icon type="plus" />
          标签
        </a-button>
        <span
          :style="{
            marginLeft: '10px',
            display: context.mode === 'detail' ? 'none' : 'inline',
          }"
        >
          [{{ context.filterCount }}/{{ maxFilterCount }}] 最多添加{{ maxFilterCount }}条
        </span>
      </div>
    </div>
  </FilterContextProvider>
</template>

<style lang="scss">
@import "./label.scss";
</style>
