<script>
import { Checkbox, InputNumber, Select } from 'ant-design-vue'

export default {
  name: 'LabelFilterRule',
  components: {
    'a-checkbox': Checkbox,
    'a-input-number': InputNumber,
    'a-select': Select,
    'a-select-option': Select.Option,
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  methods: {
    handleCheckUserTagChange(e) {
      this.value.changeRule('checkUserTag', e.target.checked)
      this.onChange(this.value)
    },

    handleDurationChange(val) {
      this.value.changeRule('checkDuration', val)
      this.onChange(this.value)
    },

    handleTimeTermChange(val) {
      this.value.changeRule('timeTerm', val)
      this.onChange(this.value)
    },
  },
}
</script>

<template>
  <div class="filter-rule">
    <a-checkbox
      :checked="value.checkUserTag"
      @change="handleCheckUserTagChange"
    >
      启用用户标签规则
    </a-checkbox>

    <div v-if="value.checkUserTag" style="margin-top: 8px; display: flex; gap: 8px; align-items: center">
      <span>持续时间：</span>
      <a-input-number
        :value="value.checkDuration"
        :min="1"
        style="width: 80px"
        @change="handleDurationChange"
      />
      <a-select
        :value="value.timeTerm"
        style="width: 80px"
        @change="handleTimeTermChange"
      >
        <a-select-option value="HOUR">
          小时
        </a-select-option>
        <a-select-option value="DAY">
          天
        </a-select-option>
        <a-select-option value="WEEK">
          周
        </a-select-option>
        <a-select-option value="MONTH">
          月
        </a-select-option>
      </a-select>
    </div>
  </div>
</template>

<style scoped>
.filter-rule {
  margin-top: 8px;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}
</style>
