<script>
import { But<PERSON>, <PERSON> } from 'ant-design-vue'

export default {
  name: 'CalendarComponent',
  components: {
    'a-button': Button,
    'a-select': Select,
    'a-select-option': Select.Option,
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    mode: {
      type: String,
      default: 'edit',
    },
  },
  data() {
    return {
      showCalendar: false,
      calendarList: [
        { id: 1, name: '工作日历' },
        { id: 2, name: '节假日历' },
        { id: 3, name: '自定义日历' },
      ],
    }
  },
  methods: {
    handleCalendarChange(calendarId) {
      if (calendarId) {
        const calendar = this.calendarList.find(c => c.id === calendarId)
        this.value.changeExCalendar(calendar || {})
      }
      else {
        this.value.changeExCalendar({})
      }
      this.onChange(this.value)
    },
  },
}
</script>

<template>
  <div class="calendar-component">
    <a-button
      v-if="mode === 'edit'"
      size="small"
      @click="showCalendar = !showCalendar"
    >
      {{ showCalendar ? '隐藏日历' : '显示日历' }}
    </a-button>

    <div v-if="mode === 'detail'" class="calendar-info">
      <span v-if="value.exCalendar && value.exCalendar.name">
        排除日历：{{ value.exCalendar.name }}
      </span>
    </div>

    <div v-if="showCalendar && mode === 'edit'" class="calendar-selector">
      <a-select
        placeholder="选择要排除的日历"
        :value="value.exCalendar?.id"
        style="width: 200px"
        allow-clear
        @change="handleCalendarChange"
      >
        <a-select-option
          v-for="calendar in calendarList"
          :key="calendar.id"
          :value="calendar.id"
        >
          {{ calendar.name }}
        </a-select-option>
      </a-select>
    </div>
  </div>
</template>

<style scoped>
.calendar-component {
  display: inline-block;
}

.calendar-selector {
  margin-top: 8px;
}

.calendar-info {
  font-size: 12px;
  color: #666;
}
</style>
