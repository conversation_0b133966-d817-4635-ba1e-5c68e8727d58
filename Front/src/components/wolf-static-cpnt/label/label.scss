// @import '~antd/dist/antd.css';
@import '../../variable.scss';
$connectLineStyle: 1px dotted #ccc;
$connectLineMarginTopAndBottom: 40px;

.wolf-static-component_filter_FilterGroupPanel_label {
  .FilterGroupPanel1 {
    // border: 1px solid #ccc;
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;

    .ConnectorPanel1 {
      // border: 1px solid #ccc;
      width: 40px;
      min-width: 40px;
      position: relative;

      .VLine1 {
        position: absolute;
        top: $connectLineMarginTopAndBottom;
        bottom: $connectLineMarginTopAndBottom;
        left: 50%;
        width: 0px;
        border-left: $connectLineStyle;
        z-index: 100;
      }

      .TopLine1 {
        position: absolute;
        top: $connectLineMarginTopAndBottom;
        left: 50%;
        height: 0;
        width: 50%;
        border-top: $connectLineStyle;
        z-index: 100;
      }

      .BottomLine1 {
        position: absolute;
        bottom: $connectLineMarginTopAndBottom;
        left: 50%;
        height: 0;
        width: 50%;
        border-top: $connectLineStyle;
        z-index: 100;
      }

      .Connector1 {
        position: absolute;
        left: 0;
        top: 50%;
        height: 30px;
        line-height: 30px;
        transform: translateY(-15px);
        vertical-align: middle;
        z-index: 200;

        .ant-switch {
          background-color: #ccc;
          opacity: 1;
        }

        .ant-switch-checked {
          background-color: $primary_color;
          opacity: 1;
        }

        .ant-switch-disabled {
          opacity: 1;
        }
      }
    }

    .FilterList1 {
      // border: 1px solid #ccc;
      flex: auto;
      margin: 0;
      padding: 0;
      margin: 2px 0;

      &.inner {
        background-color: #fafafa;
      }

      >li {
        margin: 2px 2px;
        list-style: none;
      }

      .FilterSingle.edit {
        .takePlace {
          min-width: 50px;
          position: absolute;
          visibility: hidden;
          width: auto;
          white-space: nowrap;
        }

        .has-error .ant-picker {
          border-color: red;
        }
      }

      .FilterSingle {
        padding: 12px;
        line-height: 34px;

        &:focus {
          outline: none;
        }

        >div {
          display: inline-block;

          >div {
            display: inline-block;
          }

          .FilterField,
          .FilterOperator,
          .FilterValue {
            .FilterSingleWrapper {
              word-wrap: break-word;
              margin-right: 5px;

              .valueShow {
                color: $active_color;
                word-break: break-word;
              }
            }
          }

          .Ctroller {
            font-size: 12px;
            // line-height: 28px;
            display: flex;
            align-items: flex-start !important;
            margin-top: 8px;

            .add {
              margin: 0 2px;
              color: rgba(0, 0, 0, 0.65);
            }

            .add:hover {
              color: $primary_color;
            }

            .delete {
              margin: 0 4px;
              color: rgba(0, 0, 0, 0.65);
            }

            .delete:hover {
              color: $primary_color;
            }
          }

          .Validator {
            color: $danger_color;
            font-size: 16px;
          }
        }
      }
    }

    .fieldDirectoryTree {
      .ant-tree-node-content-wrapper {
        display: flex;
        overflow-x: hidden;
        padding-bottom: 4px;
      }

      .ant-tree-title {
        display: inline-block;
        max-width: 200px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  .cpnt-label {
    .labelTree {
      .ant-spin-nested-loading {
        .ant-spin .ant-spin-dot {
          margin-top: 64px;
        }
      }
    }

    .cpnt-label-remark {
      margin: 0 auto;
      padding-top: 16px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }
  }

  .clickWrapper {
    display: flex;
    align-items: center;
    position: relative;

    .anticon.anticon-down {
      position: absolute;
      right: 12px;
    }
  }
}

.relative-time-modal {
  .relative-time-btn {
    .relative-time-item {
      width: 100%;
      display: flex;
      align-items: center;

      .ant-btn {
        width: 30%;
        margin-bottom: 16px;
        border-radius: 6px;
      }
    }
  }

  .relative-time-input {
    .ant-select {
      .ant-select-selector {
        border-radius: 6px !important;
      }
    }

    .ant-input-number {
      .ant-input-number-handler-wrap {
        border-radius: 6px;
      }
    }
  }
}