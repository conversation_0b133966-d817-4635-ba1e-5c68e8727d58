import _ from 'lodash'
import moment from 'moment'
import Log from '../../utils/log'
import { getString } from '../selectTime/config'
import FilterConfig from './FilterConfig'

const {
  operatorList,
  validator,
  typeFormatter,
  DATE_TYPE_MAP,
  relativeTimeObj,
  userGroupOperatorList,
} = FilterConfig

const log = Log.getLogger('FilterModel')

class FilterModel {
  static counter = 0

  static get OPERATOR_MAP() {
    const _operatorList = location.href.includes('userGroup')
      ? userGroupOperatorList
      : operatorList
    return _operatorList.reduce((map, obj) => {
      map[obj.operator] = obj
      return map
    }, {})
  }

  constructor(
    id,
    label,
    displayName,
    operator,
    value,
    times,
    dateType,
    fieldType,
    showValue,
    checkUserTag,
    checkDuration,
    timeTerm,
    timeType,
    exCalendar,
  ) {
    this.id = id
    this.label = label
    this.displayName = displayName
    this.operator = operator
    this.value = value
    this.times = times || 0
    this.fieldType = fieldType
    this.dateType = dateType || ''
    this.showValue = showValue
    this.checkUserTag = checkUserTag || false
    this.timeType = timeType || 'DAY'
    this.exCalendar = exCalendar || {}
    // 根据条件设置属性
    if (this.checkUserTag) {
      this.checkDuration = checkDuration || 1
      this.timeTerm = timeTerm || 'HOUR'
    }
    log.debug('constructor', JSON.stringify(this))
  }

  static fromJson({
    id,
    label,
    displayName,
    operator,
    value,
    dateType,
    times,
    fieldType,
    showValue,
    checkUserTag,
    checkDuration,
    timeTerm,
    timeType,
    exCalendar,
  }) {
    log.debug(
      'fromJson',
      JSON.stringify({
        id,
        label,
        displayName,
        operator,
        dateType,
        value,
        times,
        fieldType,
        showValue,
        checkUserTag,
        checkDuration,
        timeTerm,
        timeType,
        exCalendar,
      }),
    )
    return new FilterModel(
      id,
      label,
      displayName,
      operator,
      value,
      times,
      dateType,
      fieldType,
      showValue,
      checkUserTag,
      checkDuration,
      timeTerm,
      timeType,
      exCalendar,
    )
  }

  toJson() {
    const {
      id,
      label,
      displayName,
      operator,
      value,
      times,
      fieldType,
      dateType,
      showValue,
      checkUserTag,
      checkDuration,
      timeTerm,
      timeType,
      exCalendar,
    } = this
    return {
      id,
      label,
      displayName,
      operator,
      value,
      times,
      fieldType,
      dateType,
      showValue,
      checkUserTag,
      checkDuration,
      timeTerm,
      timeType,
      exCalendar,
    }
  }

  setDirty(isDirty) {
    this.isDirty = isDirty
  }

  /**
   * 校验filterModel，返回{
   *  isValid: (boolean) 是否有效
   *  fieldType: 表示fieldType有错误，次值为错误返回值
   *  operator: 同上
   *  value: {
   *    maxLen: 错误信息
   *    required: 错误信息
   *  }
   * }
   */
  valid() {
    const { id, value, times, operator, fieldType = 'STRING', dateType } = this
    const splitValue = v => (_.isString(v) ? v.split('-') : [undefined, undefined])
    const result = { isValid: true, message: [] }
    if (!id) {
      result.message.push('请选择过滤属性')
      result.id = true
      result.isValid = false
    }

    const typeValidator = validator[fieldType]

    if (id && !typeValidator) {
      if (fieldType) {
        log.info('没有找到对应的校验器', `fieldType=${fieldType}`)
      }
      result.message.push('选择的过滤属性类型不存在')
      result.id = true
      result.isValid = false
    }

    if (!operator) {
      result.message.push('请输入操作符')
      result.operator = true
      result.isValid = false
    }
    if (
      (operator === 'DATE_FORMAT_BETWEEN' || operator === 'DATE_FORMAT_EQ')
      && typeValidator
    ) {
      if (!value) {
        result.message.push('请填写完整')
        result.value = true
        result.isValid = false
      }
      else {
        const valuesToCheck = operator === 'DATE_FORMAT_BETWEEN' ? [...value] : [value]
        const allUndefined = valuesToCheck
          .map(splitValue)
          .flat()
          .some(part => part === 'undefined')

        if (allUndefined) {
          result.message.push('请填写完整')
          result.value = true
          result.isValid = false
        }
      }
    }
    else if (
      !['ALL', 'IS_NULL', 'IS_NOT_NULL', 'IS_TRUE', 'IS_FALSE'].includes(
        operator,
      )
      && typeValidator
    ) {
      if (!value) {
        result.message.push('请选择标签值')
        result.value = true
        result.isValid = false
      }
      else if (_.isArray(value)) {
        // 数组类型返回验证器, 如果有错误的，返回一个
        if (value.length) {
          const valueValidators = value
            .map(v => this._typeValueValid(typeValidator, v))
            .filter(v => v !== null)
          if (valueValidators.length > 0) {
            result.value = true
            result.message.push(Object.values(valueValidators[0])[0])
            result.isValid = false
          }
        }
        else {
          result.value = true
          result.message.push('请输入')
          result.isValid = false
        }
      }
      else if (_.isString(value)) {
        const pre = /^\s+/g
        const suffix = /\s+$/g
        if (pre.test(value) || suffix.test(value)) {
          result.value = true
          result.message.push('标签值前后不能有空格')
          result.isValid = false
        }
      }
      else {
        const data = this._typeValueValid(typeValidator, value)
        if (data) {
          result.value = true
          result.message.push(Object.values(data)[0])
          result.isValid = false
        }
      }
    }
    // else if (!value || value.length === 0) {
    //   result.message.push('请选择标签值');
    //   result.value = true;
    //   result.isValid = false;
    // }

    if (dateType !== 'LATEST') {
      if (!dateType) {
        result.message.push('请选择时间类型')
        result.dateType = true
        result.isValid = false
      }

      if (times === null || times === undefined) {
        result.message.push('请选择更新时间')
        result.times = true
        result.isValid = false
      }
    }

    return result
  }

  /**
   * 类型验证器，可以验证STRING, LONG , DOUBLE等数据类型
   * @param {object} typeValidator {required, minLen, maxLen, regex, min, max}
   * @param {object} value 被验证的值
   */
  _typeValueValid(typeValidator, value) {
    const { required, minLen, maxLen, regex, min, max } = typeValidator.option
    const errMessage = typeValidator.message

    const message = {}

    if (
      required
      && (value === null
        || value === undefined
        || value === ''
        || (Array.isArray(value) && value.length === 0))
    ) {
      message.required = errMessage.required
    }

    if (minLen !== null || minLen !== undefined) {
      if (
        value !== null
        && value !== undefined
        && value !== ''
        && value.toString().length < minLen
      ) {
        message.minLen = errMessage.minLen
      }
    }

    if (maxLen !== null || maxLen !== undefined) {
      if (
        value !== null
        && value !== undefined
        && value !== ''
        && value.toString().length > maxLen
      ) {
        message.maxLen = errMessage.maxLen
      }
    }

    if (regex && !new RegExp(regex).test(value)) {
      message.regex = errMessage.regex
    }

    if (min !== null && min !== undefined && Number.parseInt(value) < min) {
      message.min = errMessage.min
    }

    if (max !== null && max !== undefined && Number.parseInt(value) > max) {
      message.max = errMessage.max
    }

    log.debug('_typeValueValid', typeValidator, value, message)

    if (_.isEmpty(message)) {
      return null
    }

    return message
  }

  changeProperty(property) {
    this.id = property.id
    this.label = property.label
    this.displayName = property.displayName
    this.fieldType = property.fieldType
    this.tagInfo = property.tagInfo
    this.operator = property.operator
    this.value = property.value
    this.showValue = property.showValue
    // this.dateType = null;
    // this.times = null;
  }

  changeOperator(operator) {
    this.operator = operator
    this.value = null
    this.showValue = null
  }

  changeDateType(type, resetTimes) {
    this.dateType = type
    if (resetTimes) {
      if (type === 'RELATIVE') {
        this.times = 0
      }
      else {
        this.times = null
      }
    }
  }

  /**
   *
   * @param {("checkUserTag" | "checkDuration" | "timeTerm")} type
   * @param {*} value
   * @returns
   */
  changeRule(type, value) {
    const propertyMap = {
      checkUserTag: 'checkUserTag',
      checkDuration: 'checkDuration',
      timeTerm: 'timeTerm',
    }
    const propertyName = propertyMap[type]
    if (propertyName) {
      this[propertyName] = value
    }
  }

  changeExCalendar(exCalendar) {
    this.exCalendar = exCalendar
  }

  clearProperty() {
    this.id = null
    this.label = null
    this.displayName = null
    this.fieldType = null
  }

  /**
   *
   * @description 获取时间切片名称
   */
  getTimeslicingName() {
    return (
      FilterModel.OPERATOR_MAP?.TIMESLICING?.children.find(
        v => v?.operator === this?.operator,
      )?.name || ''
    )
  }

  getOperatorShow() {
    return (
      FilterModel.OPERATOR_MAP[this.operator]?.name || this.getTimeslicingName()
    )
  }

  getDateTypeShow() {
    return DATE_TYPE_MAP[this.dateType]
  }

  getTimeShow() {
    if (this.dateType === 'ABSOLUTE') {
      return moment(this.times).format('YYYY-MM-DD')
    }
    else if (this.dateType === 'RELATIVE') {
      if (this.timeType) {
        switch (this.timeType) {
          case 'DAY':
            return relativeTimeObj[this.times] || `${this.times} 天前`
          case 'MONTH':
            return `上${this.times === 1 ? '' : this.times}${this.times === 1 ? '' : '个'}月末`
          case 'YEAR':
            return `上${this.times === 1 ? '' : this.times} 年末`
          default:
            return ''
        }
      }
      return relativeTimeObj[this.times] || `${this.times} 天前`
    }
    return null
  }

  getValueShow() {
    const { value, operator, fieldType, showValue } = this

    if (!value) {
      return ''
    }

    switch (operator) {
      case 'EQ':
      case 'NE':
      case 'GT':
      case 'GTE':
      case 'LT':
      case 'LTE':
        if (
          [
            'DATE',
            'DATETIME',
            'TIMESTAMP',
            'HIVE_DATE',
            'HIVE_TIMESTAMP',
          ].indexOf(fieldType) >= 0
        ) {
          return `${moment(value).format(typeFormatter[fieldType])}`
        }
        return showValue || value
      case 'BETWEEN':
        if (
          [
            'DATE',
            'DATETIME',
            'TIMESTAMP',
            'HIVE_DATE',
            'HIVE_TIMESTAMP',
          ].indexOf(fieldType) >= 0
        ) {
          return `[${moment(value[0]).format(
            typeFormatter[fieldType],
          )} ~ ${moment(value[1]).format(typeFormatter[fieldType])}]`
        }

        return `[${value[0]}-${value[1]}]`
      case 'ADVANCED_BETWEEN':
        return `[${getString(value[0], true)} - ${getString(value[1], true)}]`
      case 'IN':
      case 'NOT_IN':
        return `[${_.join(showValue || value, ',')}]`
      case 'LIKE':
      case 'NOT_LIKE':
      case 'START_WITH':
      case 'NOT_START_WITH':
      case 'END_WITH':
      case 'NOT_END_WITH':
        return showValue || value
      case 'IS_NOT_NULL':
      case 'IS_NULL':
        return ''
      case 'MONTH_EQ':
        return `${showValue || value}月`
      case 'DAY_EQ':
        return `${showValue || value}日`
      case 'DATE_FORMAT_EQ':
        return `${showValue || value}日`
      case 'MONTH_BETWEEN':
        return `[${value[0]}月 ~ ${value[1]}月]`
      case 'DAY_BETWEEN':
        return `[${value[0]}日 ~ ${value[1]}日]`
      case 'DATE_FORMAT_BETWEEN':
        return `[${value[0]}日 ~ ${value[1]}日]`
      default:
        return ''
    }
  }

  isValueCanEdit() {
    switch (this.operator) {
      case 'IS_NOT_NULL':
      case 'IS_NULL':
      case 'IS_TRUE':
      case 'IS_FALSE':
        return false
      default:
        return true
    }
  }
}

export default FilterModel
