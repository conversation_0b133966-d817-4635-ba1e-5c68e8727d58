// 简化版的selectTime配置文件

export function getString(obj) {
  if (!obj || typeof obj !== 'object')
    return ''

  if (obj.type === 'ABSOLUTE') {
    // 简化的时间格式化
    return new Date(obj.timestamp).toLocaleString()
  }
  else if (obj.type === 'RELATIVE') {
    const termMap = { MINUTE: '分钟', HOUR: '小时', DAY: '天', WEEK: '周', MONTH: '月' }
    const pastMap = { true: '之前', false: '之后' }
    return `${obj.times}${termMap[obj.timeTerm]}${pastMap[obj.isPast]}`
  }
  else if (obj.type === 'NOW') {
    return '现在'
  }
  return ''
}

export default {
  getString,
}
