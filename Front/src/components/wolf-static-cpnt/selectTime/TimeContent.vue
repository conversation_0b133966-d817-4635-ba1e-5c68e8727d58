<script>
import moment from 'moment'

export default {
  name: 'TimeContent',
  props: {
    data: {
      type: Object,
      default: null,
    },
    showTime: {
      type: Boolean,
      default: true,
    },
    isActionCollection: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      date: moment(),
      info: {
        type: 'ABSOLUTE',
        times: 1,
        timeTerm: 'DAY',
        isPast: true,
      },
    }
  },
  watch: {
    data: {
      handler(newData) {
        if (newData) {
          this.date = moment(newData.timestamp || Date.now())
          this.info = { ...newData }
        }
        else {
          this.date = moment()
          this.info = {
            type: 'ABSOLUTE',
            times: 1,
            timeTerm: 'DAY',
            isPast: true,
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    changeType(type) {
      this.info.type = type
    },

    onDateChange(date) {
      this.date = date
    },

    handleIsPastChange(value) {
      this.info.isPast = value === 'true'
    },

    onSave() {
      const result = { ...this.info }

      if (this.info.type === 'ABSOLUTE') {
        result.timestamp = this.date.valueOf()
      }
      else if (this.info.type === 'NOW') {
        result.timestamp = Date.now()
      }

      this.$emit('save', result)
    },
  },
}
</script>

<template>
  <div class="time-content">
    <a-tabs :active-key="info.type" @change="changeType">
      <a-tab-pane key="ABSOLUTE" tab="固定时间">
        <div class="absolute-time">
          <a-date-picker
            :value="date"
            :show-time="showTime"
            :format="showTime ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'"
            :style="{ width: '100%' }"
            @change="onDateChange"
          />
        </div>
        <div class="save-btn">
          <a-button type="primary" size="small" @click="onSave">
            保存
          </a-button>
        </div>
      </a-tab-pane>

      <a-tab-pane key="RELATIVE" tab="相对时间">
        <div class="relative-time">
          <a-input-group compact>
            <a-input-number v-model="info.times" :min="0" placeholder="数量" style="width: 100px" />
            <a-select v-model="info.timeTerm" style="width: 80px">
              <template v-if="isActionCollection">
                <a-select-option value="MINUTE">
                  分钟
                </a-select-option>
                <a-select-option value="HOUR">
                  小时
                </a-select-option>
              </template>
              <template v-else>
                <a-select-option value="DAY">
                  天
                </a-select-option>
                <a-select-option value="WEEK">
                  周
                </a-select-option>
                <a-select-option value="MONTH">
                  月
                </a-select-option>
              </template>
            </a-select>
            <a-select :value="String(info.isPast)" style="width: 80px" @change="handleIsPastChange">
              <a-select-option value="true">
                之前
              </a-select-option>
              <a-select-option value="false">
                之后
              </a-select-option>
            </a-select>
          </a-input-group>
        </div>
        <div class="save-btn">
          <a-button
            type="primary"
            size="small"
            :disabled="(!info.times && info.times !== 0) || !info.timeTerm || info.isPast === undefined"
            @click="onSave"
          >
            保存
          </a-button>
        </div>
      </a-tab-pane>

      <a-tab-pane key="NOW" tab="现在">
        <div class="now-time">
          <p>点击保存即选择了当前时间</p>
        </div>
        <div class="save-btn">
          <a-button type="primary" size="small" :disabled="info.type !== 'NOW'" @click="onSave">
            保存
          </a-button>
        </div>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<style scoped>
.time-content {
  padding: 10px;
}

.absolute-time,
.relative-time,
.now-time {
  margin-bottom: 15px;
}

.save-btn {
  text-align: right;
}

.now-time p {
  margin: 0;
  padding: 20px 0;
  text-align: center;
  color: #666;
}
</style>
