<script>
import { Switch } from 'ant-design-vue'
import FilterConfig from './FilterConfig'

// 连接器
const FILTER_CONNECTOR = FilterConfig.connector
// 连接器map
const FILTER_CONNECTOR_SWITCH_MAP = {
  true: FILTER_CONNECTOR[0],
  false: FILTER_CONNECTOR[1],
}

const FILTER_CONNECTOR_SWITCH_MAP_REVERSE = {}
FILTER_CONNECTOR_SWITCH_MAP_REVERSE[FILTER_CONNECTOR[0].value] = true
FILTER_CONNECTOR_SWITCH_MAP_REVERSE[FILTER_CONNECTOR[1].value] = false

export default {
  name: 'ActionCollectiveFilterConnector',
  components: {
    'a-switch': Switch,
  },
  props: {
    value: {
      type: String,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    mode: {
      type: String,
      default: 'edit',
    },
  },
  data() {
    return {
      FILTER_CONNECTOR_SWITCH_MAP,
      FILTER_CONNECTOR_SWITCH_MAP_REVERSE,
    }
  },
  methods: {
    handleChange(v) {
      this.onChange(FILTER_CONNECTOR_SWITCH_MAP[v].value)
    },
  },
}
</script>

<template>
  <a-switch
    size="small"
    :checked-children="FILTER_CONNECTOR_SWITCH_MAP.true.name"
    :un-checked-children="FILTER_CONNECTOR_SWITCH_MAP.false.name"
    :checked="FILTER_CONNECTOR_SWITCH_MAP_REVERSE[value]"
    :disabled="mode === 'detail'"
    class="cpntSwitch"
    @change="handleChange"
  />
</template>

<style scoped>
/* 样式将从filter.scss中继承 */
</style>
