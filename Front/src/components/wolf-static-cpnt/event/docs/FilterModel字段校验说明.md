# FilterModel 字段校验说明

## 概述

FilterModel 现在支持基于 `firstAction` 字段的3套不同校验逻辑，新增了多个字段来支持复杂的事件过滤场景。

## 新增字段

### 核心字段
- `firstAction`: 主要事件类型 (DONE | FIRST_DO_LAST_NOT_DO | DO_SEQ)
- `firstTimeValue`: 第一个时间值
- `firstTimeUnit`: 第一个时间单位
- `lastTimeValue`: 最后时间值
- `lastTimeUnit`: 最后时间单位
- `lastAction`: 最后事件动作
- `lastEventInfo`: 最后事件信息
- `lastEventFilterProperty`: 最后事件过滤属性
- `todayDoEvents`: 依次做过事件列表

## 三套校验逻辑

### 1. DONE (当天做过)
**适用场景**: 普通的单事件过滤

**必填字段**:
- `action`: 事件行为
- `eventInfo.id`: 事件ID
- `firstTimeValue`: 时间值 (> 0)
- `firstTimeUnit`: 时间单位
- `eventAggregateProperty.propertyType`: 属性类型
- `eventAggregateProperty.operator`: 操作符

**校验规则**:
```javascript
_validateTodayDo(result) {
  // 基础字段校验
  this._validateBasicFields(result)
  // 时间相关校验  
  this._validateTimeFields(result)
  // 事件属性校验
  this._validateEventProperties(result)
}
```

### 2. FIRST_DO_LAST_NOT_DO (先做过，后未做过)
**适用场景**: 两个事件的时序关系过滤

**必填字段**:
- `action`: 事件行为
- `eventInfo.id`: 第一个事件ID  
- `lastEventInfo.id`: 最后一个事件ID
- `lastAction`: 必须是 'NOT_DO'
- `lastTimeValue`: 时间值 (> 0)

**校验规则**:
```javascript
_validateFirstDoLastNotDo(result) {
  // 基础字段校验
  this._validateBasicFields(result)
  // 第一个事件必须选择
  // 最后一个事件必须选择
  // 最后事件action必须是NOT_DO
  // 时间值必须有效
}
```

### 3. DO_SEQ (依次做过)
**适用场景**: 多个事件的顺序执行过滤

**必填字段**:
- `action`: 必须是 'DO_SEQ'
- `firstTimeValue`: 时间值 (> 0)
- `firstTimeUnit`: 时间单位
- `todayDoEvents`: 至少包含一个有效事件

**校验规则**:
```javascript
_validateTodayDoSeq(result) {
  // action必须是DO_SEQ
  // 时间相关校验
  this._validateTimeFields(result)
  // 至少有一个事件
  // 每个事件都必须有有效的eventInfo.id
}
```

## 字段映射关系

### firstAction 与 action 的关系
```javascript
const FIELD_MAPPING = {
  'DONE': 'DONE',           // 做过
  'FIRST_DO_LAST_NOT_DO': 'FIRST_DO',  // 先做过
  'DO_SEQ': 'DO_SEQ'      // 依次做过
}
```

### 界面显示逻辑
```javascript
// 是否显示时间设置
shouldShowTimeSettings() {
  return this.firstAction !== 'FIRST_DO_LAST_NOT_DO'
}

// 是否显示最后事件设置  
shouldShowLastEventSettings() {
  return this.firstAction === 'FIRST_DO_LAST_NOT_DO'
}

// 是否显示依次做过事件列表
shouldShowTodayDoEventsList() {
  return this.firstAction === 'DO_SEQ' && this.action === 'DO_SEQ'
}
```

## 数据结构示例

### DONE 模式
```json
{
  "firstAction": "DONE",
  "action": "DONE",
  "eventInfo": { "id": 123, "displayName": "登录" },
  "firstTimeValue": 1,
  "firstTimeUnit": "HOUR",
  "eventAggregateProperty": {
    "propertyType": "TIMES",
    "operator": "GT", 
    "value": 5
  }
}
```

### FIRST_DO_LAST_NOT_DO 模式
```json
{
  "firstAction": "FIRST_DO_LAST_NOT_DO", 
  "action": "FIRST_DO",
  "eventInfo": { "id": 123, "displayName": "注册" },
  "lastEventInfo": { "id": 456, "displayName": "购买" },
  "lastAction": "NOT_DO",
  "lastTimeValue": 7,
  "lastTimeUnit": "DAY"
}
```

### DO_SEQ 模式
```json
{
  "firstAction": "DO_SEQ",
  "action": "DO_SEQ", 
  "firstTimeValue": 24,
  "firstTimeUnit": "HOUR",
  "todayDoEvents": [
    {
      "eventInfo": { "id": 123, "displayName": "登录" },
      "eventFilterProperty": { "connector": "AND", "filters": [] }
    },
    {
      "eventInfo": { "id": 456, "displayName": "浏览商品" },
      "eventFilterProperty": { "connector": "AND", "filters": [] }
    }
  ]
}
```

## 迁移指南

### 兼容性处理
旧版本的FilterModel会自动使用`DONE`模式校验，确保向后兼容。

### 废弃方法
- `isFirstDoLastNotDo()` → 使用 `shouldShowLastEventSettings()`
- `isTodayDo()` → 使用 `shouldShowTodayDoEventsList()`

### 新增辅助方法
- `getFirstEventDisplayName()`: 获取当前模式显示名称
- `getValidationErrors()`: 获取详细验证错误信息  
- `resetToDefault()`: 重置到默认状态

## 注意事项

1. **字段初始化**: 构造函数会根据`action`自动初始化依次做过事件
2. **校验时机**: 每次字段变更后都应该调用`valid()`方法检查
3. **错误处理**: 使用`getValidationErrors()`获取结构化的错误信息
4. **性能考虑**: 大量依次做过事件时注意性能优化

## 测试用例

```javascript
// 测试TODAY_DO模式
const model1 = new FilterModel('DONE', eventInfo, ...)
model1.firstAction = 'DONE'
const result1 = model1.valid()

// 测试FIRST_DO_LAST_NOT_DO模式  
const model2 = new FilterModel('FIRST_DO', ...)
model2.firstAction = 'FIRST_DO_LAST_NOT_DO'
model2.lastAction = 'NOT_DO'
const result2 = model2.valid()

// 测试TODAY_DO_SEQ模式
const model3 = new FilterModel('DO_SEQ', ...)  
model3.firstAction = 'DO_SEQ'
model3.todayDoEvents = [...]
const result3 = model3.valid()
``` 