import { getValidationMessage, hasValidationError } from 'wolf-static-cpnt/event/utils'

/**
 * 验证逻辑混入
 * 处理组件的验证相关逻辑
 */
export const validationMixin = {
  data() {
    return {
      validator: {},
    }
  },

  computed: {
    hasValidationError() {
      return hasValidationError(this.validator)
    },

    validationMessage() {
      return getValidationMessage(this.validator)
    },
  },

  watch: {
    // 监听关键字段变化，触发验证更新
    action() {
      this.updateValidator()
    },
    'eventInfo.id': function () {
      this.updateValidator()
    },
    'eventAggregateProperty.value': function () {
      this.updateValidator()
    },
    'eventAggregateProperty.propertyType': function () {
      this.updateValidator()
    },
    'dateRange': {
      handler() {
        this.updateValidator()
      },
      deep: true,
    },
    'eventAggregateProperty.fun': function () {
      this.updateValidator()
    },
    'eventAggregateProperty.operator': function () {
      this.updateValidator()
    },
    'value.firstAction': function () {
      this.updateValidator()
    },
    'value.firstTimeValue': function () {
      this.updateValidator()
    },
    'value.firstTimeUnit': function () {
      this.updateValidator()
    },
    'value.lastTimeValue': function () {
      this.updateValidator()
    },
    'value.lastTimeUnit': function () {
      this.updateValidator()
    },
    'value.lastAction': function () {
      this.updateValidator()
    },
    'value.lastEventInfo': {
      handler() {
        this.updateValidator()
      },
      deep: true,
    },
    'value.todayDoEvents': {
      handler() {
        this.updateValidator()
      },
      deep: true,
    },
  },

  mounted() {
    this.updateValidator()
  },

  methods: {
    /**
     * 更新验证器状态
     */
    updateValidator() {
      if (this.mode !== 'edit')
        return
      this.validator = this.value.valid()
    },

    /**
     * 验证组件有效性
     * @returns {boolean} 是否有效
     */
    isValid() {
      // 实时获取最新的验证结果，而不是使用缓存的validator
      const currentValidator = this.value.valid()

      const isValidFirst = this._validateFirstFilter()
      const isValidLast = this._validateLastFilter()
      const isValidTodayDo = this._validateTodayDoFilters()

      // 使用实时验证结果判断model层的有效性
      const isModelValid = currentValidator.isValid

      const finalResult = isModelValid && isValidFirst && isValidLast && isValidTodayDo

      // 如果任何校验失败，设置validating状态以显示错误UI
      if (!finalResult) {
        this.value.validating = true
      }

      return finalResult
    },

    /**
     * 验证第一个过滤器
     * @private
     */
    _validateFirstFilter() {
      if (!this.$refs.filterRef) {
        return true
      }
      const isValid = this.$refs.filterRef.isValid && this.$refs.filterRef.isValid(true)

      // 如果BaseFilter校验失败，需要设置EventModel的validating状态
      if (!isValid) {
        this.value.validating = true
      }

      return isValid
    },

    /**
     * 验证最后事件过滤器
     * @private
     */
    _validateLastFilter() {
      if (!this.isFirstDoLastNotDo || !this.$refs.lastFilterRef) {
        return true
      }
      const isValid = this.$refs.lastFilterRef.isValid && this.$refs.lastFilterRef.isValid(true)

      // 如果BaseFilter校验失败，需要设置EventModel的validating状态
      if (!isValid) {
        this.value.validating = true
      }

      return isValid
    },

    /**
     * 验证依次做过事件的过滤器
     * @private
     */
    _validateTodayDoFilters() {
      if (!this.isTodayDoSeq || this.todayDoEvents.length === 0) {
        return true
      }

      for (let i = 0; i < this.todayDoEvents.length; i++) {
        const filterRef = this.$refs[`todayDoFilterRef_${i}`]
        if (filterRef && filterRef[0]) {
          const isValidTodayDoEvent = filterRef[0].isValid && filterRef[0].isValid(true)
          if (!isValidTodayDoEvent) {
            // 如果BaseFilter校验失败，需要设置EventModel的validating状态
            this.value.validating = true
            return false
          }
        }
      }
      return true
    },
  },
}
