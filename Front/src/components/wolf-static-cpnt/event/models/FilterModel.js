import _ from 'lodash'
import moment from 'moment'
import { FilterConfig } from 'wolf-static-cpnt/event/config'
import Log from '@/components/utils/log'

// 常量定义
const MAX_TODAY_DO_EVENTS = 5

// eslint-disable-next-line no-unused-vars
const { operatorList, _validator, DATE_TYPE_MAP, relativeTimeObj, validator } = FilterConfig
// 操作符map
const OPERATOR_MAP = operatorList.reduce((map, obj) => {
  map[obj.operator] = obj
  return map
}, {})

const log = Log.getLogger('FilterModel')

class FilterModel {
  static counter = 0

  constructor(
    action,
    eventInfo,
    eventAggregateProperty,
    eventFilterProperty,
    key,
    firstAction,
    firstTimeValue,
    firstTimeUnit,
    lastTimeValue,
    lastTimeUnit,
    lastAction,
    lastEventInfo,
    lastEventFilterProperty,
    todayDoEvents,
    pushData,
  ) {
    this.action = action
    this.eventInfo = eventInfo
    this.eventAggregateProperty = eventAggregateProperty
    this.eventFilterProperty = eventFilterProperty
    this.key = key
    this.firstAction = firstAction || 'DONE' // 默认值为"当天做过"
    this.firstTimeValue = firstTimeValue || 1 // 默认值为1
    this.firstTimeUnit = firstTimeUnit || 'HOUR' // 默认值为"小时"
    this.lastTimeValue = lastTimeValue || 1 // 默认值为1
    this.lastTimeUnit = lastTimeUnit || 'HOUR'
    this.lastAction = firstAction === 'FIRST_DO_LAST_NOT_DO' ? 'NOT_DO' : lastAction || undefined
    this.lastEventInfo = lastEventInfo
    this.lastEventFilterProperty = lastEventFilterProperty
    this.todayDoEvents = todayDoEvents || []
    this.pushData = pushData || false // 默认值为false

    // 如果是依次做过模式且没有事件，初始化一个空事件
    if (this.action === 'DO_SEQ' && (!this.todayDoEvents || this.todayDoEvents.length === 0)) {
      this.todayDoEvents = [{
        eventInfo: {},
        eventFilterProperty: {
          connector: 'AND',
          filters: [],
        },
      }]
    }

    log.debug('constructor', JSON.stringify(this))
  }

  static fromJson({
    action,
    eventInfo,
    eventAggregateProperty,
    eventFilterProperty,
    firstAction,
    firstTimeValue,
    firstTimeUnit,
    lastTimeValue,
    lastTimeUnit,
    lastAction,
    lastEventInfo,
    lastEventFilterProperty,
    todayDoEvents,
    pushData,
  }) {
    log.debug(
      'fromJson',
      JSON.stringify({
        action,
        eventInfo,
        eventAggregateProperty,
        eventFilterProperty,
        firstAction,
        firstTimeValue,
        firstTimeUnit,
        lastTimeValue,
        lastTimeUnit,
        lastAction,
        lastEventInfo,
        lastEventFilterProperty,
        todayDoEvents,
        pushData,
      }),
    )
    return new FilterModel(
      action,
      eventInfo,
      eventAggregateProperty,
      eventFilterProperty,
      Math.random(),
      firstAction,
      firstTimeValue,
      firstTimeUnit,
      lastTimeValue,
      lastTimeUnit,
      lastAction,
      lastEventInfo,
      lastEventFilterProperty,
      todayDoEvents,
      pushData,
    )
  }

  toJson() {
    const {
      action,
      eventInfo,
      eventAggregateProperty,
      eventFilterProperty,
      firstAction,
      firstTimeValue,
      firstTimeUnit,
      lastTimeValue,
      lastTimeUnit,
      lastAction,
      lastEventInfo,
      lastEventFilterProperty,
      todayDoEvents,
      pushData,
    } = this
    return {
      action,
      eventInfo,
      eventAggregateProperty,
      eventFilterProperty,
      firstAction,
      firstTimeValue,
      firstTimeUnit,
      lastTimeValue,
      lastTimeUnit,
      lastAction,
      lastEventInfo,
      lastEventFilterProperty,
      todayDoEvents,
      pushData,
    }
  }

  setDirty(isDirty) {
    this.isDirty = isDirty
  }

  /**
   * 是否是先做过，后未做过
   * @deprecated 使用 shouldShowLastEventSettings() 代替
   * @returns
   */
  isFirstDoLastNotDo() {
    return this.firstAction === 'FIRST_DO_LAST_NOT_DO'
  }

  /**
   * 是否是依次做过
   * @deprecated 使用 shouldShowTodayDoEventsList() 代替
   * @returns
   */
  isTodayDo() {
    return this.firstAction === 'DO_SEQ'
  }

  /**
   * 校验filterModel，根据firstEvent实现不同的校验逻辑
   * 返回{
   *  isValid: (boolean) 是否有效
   *  fieldType: 表示fieldType有错误，次值为错误返回值
   *  operator: 同上
   *  value: {
   *    maxLen: 错误信息
   *    required: 错误信息
   *  }
   * }
   */
  valid() {
    const result = { isValid: true, message: [] }

    // 根据firstEvent选择不同的校验策略
    switch (this.firstAction) {
      case 'DONE':
        return this._validateTodayDo(result)
      case 'FIRST_DO_LAST_NOT_DO':
        return this._validateFirstDoLastNotDo(result)
      case 'DO_SEQ':
        return this._validateTodayDoSeq(result)
      default:
        return this._validateTodayDo(result) // 默认使用TODAY_DO校验
    }
  }

  /**
   * 校验"当天做过"模式
   * @private
   */
  _validateTodayDo(result) {
    // 基础字段校验
    this._validateBasicFields(result)

    // 时间相关校验
    this._validateTimeFields(result)

    // 事件属性校验
    this._validateEventProperties(result)

    return result
  }

  /**
   * 校验"先做过，后未做过"模式
   * @private
   */
  _validateFirstDoLastNotDo(result) {
    // 基础字段校验
    this._validateBasicFields(result)
    // 第一个事件校验
    if (!this.eventInfo?.id) {
      result.message.push('请选择第一个事件')
      result.id = true
      result.isValid = false
    }

    // 最后一个事件校验
    if (!this.lastEventInfo?.id) {
      result.message.push('请选择最后一个事件')
      result.lastEventId = true
      result.isValid = false
    }

    // 最后事件的action必须是NOT_DO
    if (this.lastAction !== 'NOT_DO') {
      result.message.push('最后事件必须是"未做过"')
      result.lastAction = true
      result.isValid = false
    }

    // 时间值校验
    if (!this.lastTimeValue || this.lastTimeValue <= 0) {
      result.message.push('请设置有效的时间值')
      result.lastTimeValue = true
      result.isValid = false
    }

    return result
  }

  /**
   * 校验"依次做过"模式
   * @private
   */
  _validateTodayDoSeq(result) {
    // 基础action校验
    if (this.action !== 'DO_SEQ') {
      result.message.push('依次做过模式action必须为DO_SEQ')
      result.action = true
      result.isValid = false
    }

    // 时间相关校验
    this._validateTimeFields(result)

    // todayDoEvents校验
    if (!this.todayDoEvents || this.todayDoEvents.length === 0) {
      result.message.push('请至少添加一个事件')
      result.todayDoEvents = true
      result.isValid = false
    }
    else {
      // 校验每个事件都有有效的eventInfo
      for (let i = 0; i < this.todayDoEvents.length; i++) {
        const event = this.todayDoEvents[i]
        if (!event.eventInfo?.id) {
          result.message.push(`第${i + 1}个事件未选择`)
          result.todayDoEvents = true
          result.isValid = false
          break
        }
      }
    }

    return result
  }

  /**
   * 校验基础字段（通用）
   * @private
   */
  _validateBasicFields(result) {
    if (!this.action) {
      result.message.push('请选择事件行为')
      result.action = true
      result.isValid = false
    }
  }

  /**
   * 校验时间字段
   * @private
   */
  _validateTimeFields(result) {
    if (!this.firstTimeValue || this.firstTimeValue <= 0) {
      result.message.push('请设置有效的时间值')
      result.firstTimeValue = true
      result.isValid = false
    }

    if (!this.firstTimeUnit) {
      result.message.push('请选择时间单位')
      result.firstTimeUnit = true
      result.isValid = false
    }
  }

  /**
   * 校验事件属性（用于普通模式）
   * @private
   */
  _validateEventProperties(result) {
    const { eventInfo = {}, eventAggregateProperty = {} } = this
    const { id } = eventInfo
    const { propertyType, fun, operator, value, property = {} } = eventAggregateProperty
    const { fieldType, field } = property || {}

    if (!id) {
      result.message.push('请选择事件')
      result.id = true
      result.isValid = false
    }

    if (!propertyType || (propertyType === 'EVENT_PROPERTY' && !field)) {
      result.message.push('请选择事件属性')
      result.eventAggregateProperty = true
      result.isValid = false
    }

    if (propertyType === 'EVENT_PROPERTY' && !fun) {
      result.message.push('请选择计数函数')
      result.fun = true
      result.isValid = false
    }

    let typeValidator = validator[fieldType]
    if (propertyType === 'TIMES') {
      typeValidator = validator.INT
    }

    if (!operator) {
      result.message.push('请输入操作符')
      result.operator = true
      result.isValid = false
    }

    if (!['ALL', 'IS_NULL', 'IS_NOT_NULL', 'IS_TRUE', 'IS_FALSE'].includes(operator) && typeValidator) {
      if (_.isNil(value)) {
        result.message.push('请输入属性值')
        result.value = true
        result.isValid = false
      }
      else if (_.isArray(value)) {
        const valueValidators = value.map(v => this._typeValueValid(typeValidator, v)).filter(v => v !== null)
        if (valueValidators.length > 0) {
          result.value = true
          result.message.push(Object.values(valueValidators[0])[0])
          result.isValid = false
        }
      }
      else {
        const data = this._typeValueValid(typeValidator, value)
        if (data) {
          result.value = true
          result.message.push(Object.values(data)[0])
          result.isValid = false
        }
      }
    }
  }

  /**
   * 类型验证器，可以验证STRING, LONG , DOUBLE等数据类型
   * @param {object} typeValidator {required, minLen, maxLen, regex, min, max}
   * @param {object} value 被验证的值
   */
  _typeValueValid(typeValidator, value) {
    const { required, minLen, maxLen, regex, min, max } = typeValidator.option
    const errMessage = typeValidator.message

    const message = {}

    if (
      required
      && (value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0))
    ) {
      message.required = errMessage.required
    }

    if (minLen !== null || minLen !== undefined) {
      if (value !== null && value !== undefined && value !== '' && value.toString().length < minLen) {
        message.minLen = errMessage.minLen
      }
    }

    if (maxLen !== null || maxLen !== undefined) {
      if (value !== null && value !== undefined && value !== '' && value.toString().length > maxLen) {
        message.maxLen = errMessage.maxLen
      }
    }

    if (regex && !new RegExp(regex).test(value)) {
      message.regex = errMessage.regex
    }

    if (min !== null && min !== undefined && Number.parseInt(value) < min) {
      message.min = errMessage.min
    }

    if (max !== null && max !== undefined && Number.parseInt(value) > max) {
      message.max = errMessage.max
    }

    log.debug('_typeValueValid', typeValidator, value, message)

    if (_.isEmpty(message)) {
      return null
    }

    return message
  }

  changeProperty(property) {
    const {
      action,
      eventInfo,
      eventAggregateProperty,
      eventFilterProperty,
      firstAction,
      firstTimeValue,
      firstTimeUnit,
      lastTimeValue,
      lastTimeUnit,
      lastAction,
      lastEventInfo,
      lastEventFilterProperty,
      todayDoEvents,
      pushData,
    } = property
    this.action = action
    this.eventInfo = eventInfo
    this.eventAggregateProperty = eventAggregateProperty
    // this.dateRange = dateRange;
    this.eventFilterProperty = eventFilterProperty

    // 添加新字段的更新逻辑
    if (firstAction !== undefined) {
      this.firstAction = firstAction
    }
    this.firstTimeValue = firstTimeValue
    this.firstTimeUnit = firstTimeUnit
    this.lastTimeValue = lastTimeValue
    this.lastTimeUnit = lastTimeUnit
    this.lastAction = lastAction
    this.lastEventInfo = lastEventInfo
    this.lastEventFilterProperty = lastEventFilterProperty
    this.todayDoEvents = todayDoEvents
    this.pushData = pushData
  }

  changeOperator(operator) {
    if (!this.eventAggregateProperty) {
      this.eventAggregateProperty = { propertyType: 'TIMES' }
    }

    this.eventAggregateProperty = {
      ...this.eventAggregateProperty,
      operator,
      value: null,
    }
  }

  changeValue(value) {
    if (!this.eventAggregateProperty) {
      this.eventAggregateProperty = { propertyType: 'TIMES' }
    }

    this.eventAggregateProperty = {
      ...this.eventAggregateProperty,
      value,
    }
  }

  changePushData(pushData) {
    this.pushData = pushData
  }

  changePropertyValue(eventFilterProperty) {
    this.eventFilterProperty = eventFilterProperty
  }

  changeLastEventFilterProperty(lastEventFilterProperty) {
    this.lastEventFilterProperty = lastEventFilterProperty
  }

  changeEventAggregateProperty(eventAggregateProperty) {
    this.eventAggregateProperty = eventAggregateProperty
  }

  clearProperty() {
    this.action = 'DONE'
    this.eventInfo = {}
    this.eventAggregateProperty = {}
    this.eventFilterProperty = null
    this.firstAction = 'DONE'
    this.firstTimeValue = 1
    this.firstTimeUnit = 'HOUR'
    this.lastTimeValue = 1
    this.lastTimeUnit = 'HOUR'
    this.lastAction = undefined
    this.lastEventInfo = {}
    this.lastEventFilterProperty = null
    this.todayDoEvents = []
    this.pushData = false
  }

  getOperatorShow() {
    return OPERATOR_MAP[this.eventAggregateProperty?.operator]?.name
  }

  getDateTypeShow() {
    return DATE_TYPE_MAP[this.dateType]
  }

  getTimeShow() {
    if (this.dateType === 'ABSOLUTE') {
      return moment(this.times).format('YYYY-MM-DD')
    }
    else if (this.dateType === 'RELATIVE') {
      return relativeTimeObj[this.times] || `${this.times} 天前`
    }
    return null
  }

  getValueShow() {
    const { eventAggregateProperty = {} } = this
    const { value, operator } = eventAggregateProperty

    if (_.isNil(value) || _.isNaN(value)) {
      return ''
    }

    switch (operator) {
      case 'EQ':
      case 'NE':
      case 'GT':
      case 'GTE':
      case 'LT':
      case 'LTE':
      case 'LIKE':
      case 'NOT_LIKE':
      case 'START_WITH':
      case 'NOT_START_WITH':
      case 'END_WITH':
      case 'NOT_END_WITH':
        return value
      case 'IN':
      case 'NOT_IN':
        return `[${_.join(value, ',')}]`
      case 'IS_NOT_NULL':
      case 'IS_NULL':
        return ''
      case 'BETWEEN':
        return `[${value[0]}-${value[1]}]`
      default:
        return ''
    }
  }

  isValueCanEdit() {
    switch (this.operator) {
      case 'IS_NOT_NULL':
      case 'IS_NULL':
      case 'IS_TRUE':
      case 'IS_FALSE':
        return false
      default:
        return true
    }
  }

  // 添加依次做过事件
  addTodayDoEvent() {
    this._ensureTodayDoEventsExists()

    if (!this._canAddMoreEvents()) {
      console.warn('已达到最大事件数量限制:', MAX_TODAY_DO_EVENTS)
      return false
    }

    this.todayDoEvents.push(this._createEmptyTodayDoEvent())
    return true
  }

  // 检查是否可以添加更多事件
  canAddMoreTodayDoEvents() {
    return !this.todayDoEvents || this.todayDoEvents.length < MAX_TODAY_DO_EVENTS
  }

  // 删除依次做过事件
  removeTodayDoEvent(index) {
    if (this.todayDoEvents && this.todayDoEvents.length > 1) {
      this.todayDoEvents.splice(index, 1)
    }
  }

  // 更新依次做过事件
  updateTodayDoEvent(index, eventData) {
    if (this.todayDoEvents && this.todayDoEvents[index]) {
      this.todayDoEvents[index] = { ...this.todayDoEvents[index], ...eventData }
    }
  }

  // 检查是否为依次做过模式
  isTodayDoSeq() {
    return this.action === 'DO_SEQ'
  }

  /**
   * 获取当前模式的显示名称
   */
  getFirstEventDisplayName() {
    return FilterConfig.FIRST_ACTION[this.firstAction] || this.firstAction
  }

  /**
   * 检查是否需要显示时间设置
   */
  shouldShowTimeSettings() {
    return this.firstAction !== 'FIRST_DO_LAST_NOT_DO'
  }

  /**
   * 检查是否需要显示最后事件设置
   */
  shouldShowLastEventSettings() {
    return this.firstAction === 'FIRST_DO_LAST_NOT_DO'
  }

  /**
   * 检查是否需要显示依次做过事件列表
   */
  shouldShowTodayDoEventsList() {
    return this.firstAction === 'DO_SEQ' && this.action === 'DO_SEQ'
  }

  /**
   * 获取验证错误的字段列表
   */
  getValidationErrors() {
    const validResult = this.valid()
    if (validResult.isValid) {
      return []
    }

    const errors = []
    Object.keys(validResult).forEach((key) => {
      if (key !== 'isValid' && key !== 'message' && validResult[key] === true) {
        errors.push(key)
      }
    })

    return {
      fields: errors,
      messages: validResult.message || [],
    }
  }

  /**
   * 重置字段到默认状态
   */
  resetToDefault() {
    this.action = 'DONE'
    this.eventInfo = {}
    this.eventAggregateProperty = {}
    this.eventFilterProperty = null
    this.firstAction = 'DONE'
    this.firstTimeValue = 1
    this.firstTimeUnit = 'HOUR'
    this.lastTimeValue = 1
    this.lastTimeUnit = 'HOUR'
    this.lastAction = undefined
    this.lastEventInfo = {}
    this.lastEventFilterProperty = null
    this.todayDoEvents = []
    this.pushData = false
  }

  // 私有方法：确保todayDoEvents存在
  _ensureTodayDoEventsExists() {
    if (!this.todayDoEvents) {
      this.todayDoEvents = []
    }
  }

  // 私有方法：检查是否可以添加更多事件
  _canAddMoreEvents() {
    return this.todayDoEvents.length < MAX_TODAY_DO_EVENTS
  }

  // 私有方法：创建空的今日做过事件
  _createEmptyTodayDoEvent() {
    return {
      eventInfo: {},
      eventFilterProperty: {
        connector: 'AND',
        filters: [],
      },
    }
  }
}

export default FilterModel
