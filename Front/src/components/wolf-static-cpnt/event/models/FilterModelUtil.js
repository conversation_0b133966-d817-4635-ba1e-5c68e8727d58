import _ from 'lodash'
import { FilterModel } from 'wolf-static-cpnt/event'
import Log from '@/components/utils/log'

const log = Log.getLogger('FilterModelUtil')

// const defaultDateRange = [
//   { type: 'RELATIVE', timeTerm: 'DAY', isPast: true, times: 7 },
//   { type: 'NOW', timeTerm: 'DAY', isPast: true },
// ]

// const actionCollectionDateRange = [
//   { type: 'RELATIVE', timeTerm: 'DAY', isPast: true, times: 7, truncateAsDay: true },
//   { type: 'NOW', timeTerm: 'DAY', isPast: true, isEndTime: true },
// ]

export default class FilterModelUtil {
  /**
   *  创建过滤model
   *  isActionCollection 是否是行为集合分群用
   *  firstAction 指定firstAction，如果不传则使用默认值'DONE'
   */
  static createFilter(isActionCollection, firstAction) {
    // const dateRange = isActionCollection ? actionCollectionDateRange : defaultDateRange;
    const firstActionMap = {
      DONE: 'DONE',
      FIRST_DO_LAST_NOT_DO: 'FIRST_DO',
      DO_SEQ: 'DO_SEQ',
    }
    const action = firstActionMap[firstAction] || 'DONE'
    return new FilterModel(action, {}, { propertyType: 'TIMES' }, {}, Math.random(), firstAction)
  }

  /**
   * 创建过滤列表
   */
  static createFilterGroup(filters) {
    return {
      connector: 'AND',
      filters: filters || [],
    }
  }

  /**
   * 向过滤组添加过滤组
   * @param {object} filter {connector, filters:[]}
   * @return 返回新创建的过滤组
   */
  static addFilterGroup(filterGroup, filter) {
    if (!filterGroup) {
      log.warn('addFilterGroup', '试图向空过滤组添加过滤组')
      return
    }

    if (_.isEmpty(filterGroup.filters) || !_.isArray(filterGroup.filters)) {
      filterGroup.filters = []
    }
    const _filterGroup = this.createFilterGroup([filter])
    filterGroup.filters.push(_filterGroup)
    return _filterGroup
  }

  /**
   * 向过滤组添加过滤组, 并且添加一个Filter
   * @param {object} filterGroup {connector, filters:[]}
   * @param {boolean} isActionCollection 是否是行为集合分群用
   * @param {string} externalFirstAction 外部传入的firstAction，优先级高于内部获取的firstAction
   */
  static addFilterGroupWithOneFilter(filterGroup, isActionCollection, externalFirstAction) {
    // 优先使用外部传入的firstAction，如果没有则获取第一个过滤器的firstAction作为新过滤器的默认值
    const firstFilterFirstAction = externalFirstAction || this.getFirstFilterFirstAction(filterGroup)
    return this.addFilterGroup(filterGroup, this.createFilter(isActionCollection, firstFilterFirstAction))
  }

  /**
   * 向过滤组中添加过滤
   * @param {object} filterGroup {connector, filters:[]}
   * @return 返回FilterModel
   */
  static addFilter(filterGroup) {
    if (!filterGroup) {
      log.warn('addFilter', '试图向空过滤组添加过滤')
      return
    }

    if (_.isEmpty(filterGroup.filters) || !_.isArray(filterGroup.filters)) {
      filterGroup.filters = []
    }

    const _filter = this.createFilter()
    filterGroup.filters.push(_filter)
    return _filter
  }

  /**
   * 初始化一个有一个过滤组一个过滤器的FilterModel
   */
  static initCreateFilterGroup(showInitLine, isActionCollection, externalFirstAction) {
    if (showInitLine) {
      return this.createFilterGroup([this.createFilterGroup([this.createFilter(isActionCollection, externalFirstAction)])])
    }
    return this.createFilterGroup([])
  }

  static deleteEmptyFilterList(filterListGroup) {
    if (!filterListGroup || !filterListGroup.filters) {
      log.warn('deleteEmptyFilterList', '无法处理空filterListGroup')
      return
    }
    filterListGroup.filters = filterListGroup.filters.filter(v => !_.isEmpty(v.filters))
  }

  static isFilterValid(filter) {
    return filter.valid().isValid
  }

  static isFilterListGroupValid(filterListGroup, flag) {
    if (!flag && _.isEmpty(filterListGroup.filters)) {
      return false
    }
    return filterListGroup.filters
      .flatMap(v => v.filters)
      .map((v) => {
        v.validating = true
        return v
      })
      .map(v => this.isFilterValid(v))
      .reduce((a, b) => a && b, true)
  }

  static fromJson(json, externalFirstAction) {
    if (_.isEmpty(json)) {
      return this.initCreateFilterGroup(false, false, externalFirstAction)
    }
    const _json = _.cloneDeep(json)
    _json.filters.map((filterGroup) => {
      filterGroup.filters.forEach((filter, i) => {
        filterGroup.filters[i] = FilterModel.fromJson(filter, externalFirstAction)
      })
      return filterGroup
    })
    return _json
  }

  static toJson(filterListGroup) {
    if (_.isEmpty(filterListGroup)) {
      return null
    }
    const result = {}
    result.connector = filterListGroup.connector
    result.filters = filterListGroup.filters.map((filterGroup) => {
      return {
        connector: filterGroup.connector,
        filters: filterGroup.filters.map(filter => filter.toJson()),
      }
    })
    return result
  }

  static getValidJson(filterListGroup) {
    if (_.isEmpty(filterListGroup)) {
      return null
    }
    const result = {}
    result.connector = filterListGroup.connector
    result.filters = filterListGroup.filters
      .map((filterGroup) => {
        return {
          connector: filterGroup.connector,
          filters: filterGroup.filters.filter(v => v.valid().isValid).map(filter => filter.toJson()),
        }
      })
      .filter(v => !_.isEmpty(v.filters))
    if (_.isEmpty(result.filters))
      return {}
    return result
  }

  static getValidFilterListCount(filterGroup) {
    return filterGroup.filters
      .filter(v => v.valid().isValid)
      .map(() => 1)
      .reduce((a, b) => a + b, 0)
  }

  static getValidFilterListGroupCount(filterListGroup) {
    return filterListGroup.filters.map(v => this.getValidFilterListCount(v)).reduce((a, b) => a + b, 0)
  }

  /**
   * 获取filters里第一个filter的firstAction
   * @param {object} filterListGroup - 包含filters数组的过滤组对象
   * @returns {string|null} 第一个filter的firstAction，如果没有找到则返回null
   */
  static getFirstFilterFirstAction(filterListGroup) {
    if (!filterListGroup || !filterListGroup.filters || filterListGroup.filters.length === 0) {
      // log.warn('getFirstFilterFirstAction', '无法从空的filterListGroup中获取firstAction')
      return null
    }

    // 遍历第一层filters
    for (const filterGroup of filterListGroup.filters) {
      if (filterGroup && filterGroup.filters && filterGroup.filters.length > 0) {
        // 找到第一个有效的filter
        const firstFilter = filterGroup.filters[0]
        if (firstFilter && firstFilter.firstAction) {
          return firstFilter.firstAction
        }
      }
    }

    log.warn('getFirstFilterFirstAction', '没有找到有效的filter或firstAction')
    return null
  }

  /**
   * 获取actioncollevtive组件  filters里第一个filter的firstAction和总数量
   * @param {object} filterListGroup - 包含filters数组的过滤组对象
   * @returns {object|null} 包含第一个firstAction和总数量的对象，如果没有找到则返回null
   */
  static getActioncollevtiveFirstFilterFirstAction(filterListGroup) {
    if (!filterListGroup || !filterListGroup.filters || filterListGroup.filters.length === 0) {
      // log.warn('getActioncollevtiveFirstFilterFirstAction', '无法从空的filterListGroup中获取firstAction')
      return null
    }

    let firstAction = null
    let totalCount = 0

    // 遍历第一层filters
    for (const filterGroup of filterListGroup.filters) {
      if (filterGroup && filterGroup.eventGroup) {
        // 在eventGroup中查找
        const eventGroup = filterGroup.eventGroup
        if (eventGroup && eventGroup.filters && eventGroup.filters.length > 0) {
          // 遍历eventGroup的filters
          for (const eventFilterGroup of eventGroup.filters) {
            if (eventFilterGroup && eventFilterGroup.filters && eventFilterGroup.filters.length > 0) {
              for (const filter of eventFilterGroup.filters) {
                if (filter && filter.firstAction) {
                  totalCount++
                  // 记录第一个找到的firstAction
                  if (firstAction === null) {
                    firstAction = filter.firstAction
                  }
                }
              }
            }
          }
        }
      }
    }

    if (firstAction === null) {
      // log.warn('getActioncollevtiveFirstFilterFirstAction', '没有找到有效的filter或firstAction')
      return null
    }

    return {
      firstAction,
      totalCount,
    }
  }

  /**
   * 设置指定过滤器的pushData为true，并将其他所有过滤器的pushData设置为false
   * @param {object} filterListGroup - 包含filters数组的过滤组对象
   * @param {object} targetFilter - 要设置为true的目标过滤器
   * @param {boolean} pushDataValue - 要设置的pushData值
   */
  static setPushDataExclusive(filterListGroup, targetFilter, pushDataValue) {
    console.log(filterListGroup, targetFilter, pushDataValue, 'filterListGroup, targetFilter, pushDataValue')
    if (!filterListGroup || !filterListGroup.filters || !targetFilter) {
      log.warn('setPushDataExclusive', '无效的参数')
      return
    }

    // 遍历所有过滤器
    filterListGroup.filters.forEach((filterGroup) => {
      if (filterGroup && filterGroup.filters) {
        filterGroup.filters.forEach((filter) => {
          if (filter === targetFilter) {
            // 设置目标过滤器的pushData
            filter.changePushData(pushDataValue)
          }

          if (filter?.todayDoEvents) {
            filter.todayDoEvents.forEach((event, i) => {
              filter.updateTodayDoEvent(i, { pushData: false })
            })
          }
          else if (pushDataValue) {
            // 如果目标过滤器设置为true，则将其他过滤器设置为false
            filter.changePushData(false)
          }
        })
      }
    })
  }

  /**
   * 简单的 ActionCollection pushData 管理函数
   * 将指定过滤器的 pushData 设置为 true，其他所有过滤器设置为 false
   * @param {object} actionCollectionValue - ActionCollection 的完整数据结构
   * @param {object} targetFilter - 要设置为 true 的目标过滤器
   * @returns {object} 返回设置后的ActionCollection 的完整数据结构
   */
  static setActionCollectionPushDataExclusive(actionCollectionValue, targetFilter) {
    // 递归循环actionCollectionValue只要是可遍历的就遍历 然后如果有pushdata就设置false
    const setPushDataExclusive = (actionCollectionValue) => {
      if (actionCollectionValue.pushData) {
        actionCollectionValue.changePushData(false)
      }
      if (actionCollectionValue.filters) {
        actionCollectionValue.filters.forEach(setPushDataExclusive)
      }
    }
    setPushDataExclusive(actionCollectionValue)
    console.log('🚀 ~ FilterModelUtil ~ setActionCollectionPushDataExclusive ~ actionCollectionValue:', actionCollectionValue)
    if (!actionCollectionValue || !actionCollectionValue.filters || !targetFilter) {
      console.warn('setActionCollectionPushDataExclusive: 无效的参数')
      return actionCollectionValue
    }

    let targetFound = false

    // 遍历所有 eventGroup 中的所有过滤器
    actionCollectionValue.filters.forEach((filterItem, filterItemIndex) => {
      // 处理所有类型的组：eventGroup, userLabel, segment, userProperty
      const groupTypes = ['eventGroup', 'userLabel', 'segment', 'userProperty']

      groupTypes.forEach((groupType) => {
        if (filterItem[groupType] && filterItem[groupType].filters) {
          filterItem[groupType].filters.forEach((filterGroup, filterGroupIndex) => {
            if (filterGroup && filterGroup.filters) {
              // console.warn(`${groupType} filterGroup [${filterItemIndex}][${filterGroupIndex}] filters 数量:`, filterGroup.filters.length)
              filterGroup.filters.forEach((filter, filterIndex) => {
                const isTarget = filter === targetFilter

                if (isTarget) {
                  targetFound = true
                  // console.warn(`设置目标过滤器 [${filterItemIndex}][${filterGroupIndex}][${filterIndex}] pushData 为 true`)
                  if (typeof filter.changePushData === 'function') {
                    filter.changePushData(true)
                  }
                  else {
                    // console.warn('目标过滤器没有 changePushData 方法，直接设置 pushData 属性')
                    filter.pushData = true
                  }
                }
                else {
                  // console.warn(`设置其他过滤器 [${filterItemIndex}][${filterGroupIndex}][${filterIndex}] pushData 为 false`)
                  if (typeof filter.changePushData === 'function') {
                    filter.changePushData(false)
                  }
                  else {
                    // console.warn('其他过滤器没有 changePushData 方法，直接设置 pushData 属性')
                    filter.pushData = false
                  }

                  // 同时处理 todayDoEvents 中的 pushData
                  if (filter.todayDoEvents && Array.isArray(filter.todayDoEvents)) {
                    filter.todayDoEvents.forEach((_, i) => {
                      if (typeof filter.updateTodayDoEvent === 'function') {
                        filter.updateTodayDoEvent(i, { pushData: false })
                      }
                      else {
                        // console.warn('过滤器没有 updateTodayDoEvent 方法')
                      }
                    })
                  }
                }
              })
            }
          })
        }
      })
    })

    // console.warn('setActionCollectionPushDataExclusive 完成，目标过滤器是否找到:', targetFound)
    // if (!targetFound) {
    //   console.warn('警告：目标过滤器未找到！可能的原因：')
    //   console.warn('1. 目标过滤器不在当前 ActionCollection 中')
    //   console.warn('2. 对象引用不匹配')
    //   console.warn('3. 数据结构不符合预期')
    // }
    return actionCollectionValue
  }
}
