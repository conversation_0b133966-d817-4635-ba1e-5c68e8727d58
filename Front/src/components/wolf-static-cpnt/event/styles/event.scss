// @import '~antd/dist/antd.css';
@import '../../../variable.scss';
$connectLineStyle: 1px dotted #ccc;
$connectLineMarginTopAndBottom: 15px;

.wolf-static-component_filter_FilterGroupPanel_event {
  .FilterGroupPanel1 {
    // border: 1px solid #ccc;
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;

    .ConnectorPanel1 {
      // border: 1px solid #ccc;
      width: 40px;
      min-width: 40px;
      position: relative;

      .VLine1 {
        position: absolute;
        top: $connectLineMarginTopAndBottom;
        bottom: $connectLineMarginTopAndBottom;
        left: 50%;
        width: 0px;
        border-left: $connectLineStyle;
        z-index: 100;
      }

      .TopLine1 {
        position: absolute;
        top: $connectLineMarginTopAndBottom;
        left: 50%;
        height: 0;
        width: 50%;
        border-top: $connectLineStyle;
        z-index: 100;
      }

      .BottomLine1 {
        position: absolute;
        bottom: $connectLineMarginTopAndBottom;
        left: 50%;
        height: 0;
        width: 50%;
        border-top: $connectLineStyle;
        z-index: 100;
      }

      .Connector1 {
        position: absolute;
        left: 0;
        top: 50%;
        height: 30px;
        line-height: 30px;
        transform: translateY(-15px);
        vertical-align: middle;
        z-index: 200;

        .ant-switch {
          background-color: $primary_color;
          opacity: 1;
        }

        .ant-switch-checked {
          background-color: $primary_color;
          opacity: 1;
        }

        .ant-switch-disabled {
          background-color: $primary_color;
          opacity: 1;
        }
      }
    }

    .FilterList1 {
      // border: 1px solid #ccc;
      flex: auto;
      margin: 0;
      padding: 0;
      margin: 2px 0;

      &.inner {
        padding: 8px;
        background-color: #fafafa;
      }

      >li {
        margin: 2px 2px;
        list-style: none;
      }

      .FilterSingle.edit {
        .takePlace {
          min-width: 50px;
          position: absolute;
          visibility: hidden;
          width: auto;
          white-space: nowrap;
        }

        .has-error .ant-picker {
          border-color: red;
        }

        .has-error .ant-select-selector {
          border-color: red;
        }

        .has-error .ant-input-affix-wrapper {
          border-color: red;
        }
      }

      .FilterSingle {
        line-height: 34px;

        &:focus {
          outline: none;
        }

        >div {
          display: inline-block;

          >div {
            display: inline-block;
          }

          .FilterField,
          .FilterOperator,
          .FilterEventAction,
          .FilterEventSelectTime,
          .FilterValue {
            .FilterSingleWrapper {
              word-wrap: break-word;
              margin-right: 8px;

              .valueShow {
                color: $active_color;
                word-break: break-word;
              }
            }
          }
          .ant-radio-inner{
            border-color: #5478BA !important;
            &::after{
              background-color: #5478BA !important;
            }
          }
          .checkboxTitle{
            color:#9199BD !important;
            font-size: 14px;
          }

          .Ctroller {
            color:#9199BD !important;
            display: flex;
            align-items: center;
            font-size: 16px ;
            //中线对齐
            line-height: 1;
            .add-text{
              font-size: 14px;
            }

            .handleAdd {
              display: flex !important;
              gap:4px;
              align-items: center;
              cursor: pointer;

              .add {
                font-size: 12px;
                margin-right: 5px;
                margin-left: 2px;
                color: rgba(0, 0, 0, 0.65);
              }

              margin-right: 8px;
            }

            .handleAdd:hover {
              color: $primary_color;

              .add {
                color: $primary_color;
              }
            }

            // font-size: 16px;

            .delete {
              margin: 0 4px;
              color: rgba(0, 0, 0, 0.65);
            }

            .delete:hover {
              color: $primary_color;
            }

            .delete {
              margin: 0 4px;
            }

            .delete:hover {
              color: $primary_color;
            }
          }

          .Validator {
            color: $danger_color;
            font-size: 16px;
          }
        }

        .innerFilter {
          padding-left: 102px;
        }
      }
    }

    .clickWrapper {
      display: flex;
      align-items: center;
      position: relative;

      .anticon.anticon-down {
        position: absolute;
        right: 12px;
      }
    }
  }
}

.eventInfoPopover {
  .eventInfo {

    //子元素挂载到contend的样式
    .eventInfoTitle {
      font-size: 16px;
      font-weight: bold;
    }

    .eventInfoContent {
      .createInformation {
        color: rgba(0, 0, 0, 0.45);
      }

      .eventTable {
        margin-top: 16px;
        margin-bottom: 16px;
      }

      .line {
        .lineTitle {
          font-weight: bold;
          margin-bottom: 18px;
        }

        .lineChart {
          height: 280px;
        }
      }
    }
  }
}