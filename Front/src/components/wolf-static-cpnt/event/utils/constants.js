// 事件过滤组件常量定义
export const EVENT_CONSTANTS = {
  // 最大数量限制
  MAX_TODAY_DO_EVENTS: 5,
  MAX_FILTER_CONDITIONS: 10,

  // 今日做过事件最大数量（用于内部逻辑）
  _isTodayMaxCount: 5,
}

// 时间单位配置
export const TIME_TERMS = [
  { value: 'MINUTE', label: '分钟', unit: 'minutes' },
  { value: 'HOUR', label: '小时', unit: 'hours' },
  { value: 'DAY', label: '天', unit: 'days' },
  { value: 'WEEK', label: '周', unit: 'weeks' },
  { value: 'MONTH', label: '月', unit: 'months' },
]

// 时间状态映射
export const TIME_STATE_MAP = {
  true: '之前',
  false: '之后',
}

// 样式常量
export const STYLE_CONSTANTS = {
  SEQUENCE_NUMBER_STYLE: {
    margin: '8px 8px 8px 0px',
    fontWeight: 'bold',
    width: '26px',
    height: '26px',
    borderRadius: '4px',
    background: 'var(--primary-color)',
    color: '#fff',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },

  CONTROLLER_STYLE: {
    display: 'flex',
    alignItems: 'center',
    marginTop: '8px',
  },
}

// 组件模式枚举
export const COMPONENT_MODES = {
  EDIT: 'edit',
  DETAIL: 'detail',
}

// 事件类型常量
export const EVENT_ACTIONS = {
  DONE: 'DONE',
  NOT_DO: 'NOT_DO',
  DO_SEQ: 'DO_SEQ',
  FIRST_DO: 'FIRST_DO',
}

export const FIRST_ACTIONS = {
  DONE: 'DONE',
  FIRST_DO_LAST_NOT_DO: 'FIRST_DO_LAST_NOT_DO',
  DO_SEQ: 'DO_SEQ',
}
