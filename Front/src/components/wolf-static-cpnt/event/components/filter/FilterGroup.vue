<script>
import FilterConnector from './FilterConnector.vue'

export default {
  name: 'FilterGroup',
  components: {
    FilterConnector,
  },
  inject: ['filterContext'],
  props: {
    connector: {
      type: String,
      default: 'AND',
    },
    onChangeConnector: {
      type: Function,
      default: () => {},
    },
    filterCount: {
      type: Number,
      default: 0,
    },
    inner: {
      type: String,
      default: '',
    },
  },
  computed: {
    context() {
      return this.filterContext()
    },
    logProvider() {
      return (
        this.context.logProvider || {
          getLogger: () => ({ debug: console.log }),
        }
      )
    },
  },
  mounted() {
    const log = this.logProvider.getLogger('FilterGroup')
    log.debug('connector', this.connector)
  },
}
</script>

<template>
  <div class="FilterGroupPanel1">
    <div class="ConnectorPanel1" :style="{ display: filterCount <= 1 ? 'none' : 'block' }">
      <div class="TopLine1" />
      <div class="VLine1" />
      <div class="BottomLine1" />
      <div class="Connector1">
        <FilterConnector :value="connector" :on-change="onChangeConnector" />
      </div>
    </div>
    <ul :class="`FilterList1 ${inner}`">
      <slot />
    </ul>
  </div>
</template>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
