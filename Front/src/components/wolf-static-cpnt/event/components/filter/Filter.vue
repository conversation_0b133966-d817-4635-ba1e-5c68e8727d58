<script>
import _ from 'lodash'
import { FilterListGroup, FilterModelUtil } from 'wolf-static-cpnt/event'
import { FilterConfig } from 'wolf-static-cpnt/event/config'
import { FilterContextProvider } from 'wolf-static-cpnt/event/context'
import Log from '@/components/utils/log.js'
import 'wolf-static-cpnt/event/styles/event.scss'

const log = Log.getLogger('Filter')

export default {
  name: 'EventFilter',
  components: {
    FilterListGroup,
    FilterContextProvider,
  },
  inject: {
    actionCollectionData: {
      from: 'actionCollectionData',
      default: () => null,
    },
    isActionCollectionComponent: {
      from: 'isActionCollectionComponent',
      default: () => false,
    },
    actionCollectionContext: {
      from: 'actionCollectionContext',
      default: () => null,
    },
  },
  provide() {
    return {
      filterListGroup: () => this.internalValue || {},
      // 传递 ActionCollection 相关数据给子组件
      actionCollectionData: this.actionCollectionData || (() => null),
      isActionCollectionComponent: this.isActionCollectionComponent || (() => false),
      actionCollectionContext: this.actionCollectionContext || (() => null),
    }
  },
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    dataProvider: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      required: true,
    },
    mode: {
      type: String,
      default: 'edit',
    },
    showInitLine: {
      type: Boolean,
      default: false,
    },
    isActionCollection: {
      type: Boolean,
      default: false,
    },
    externalFirstAction: {
      type: [String, Object],
      default: null,
    },
    showPushData: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'event',
    },
  },
  data() {
    return {
      // 内部状态，对应 React 版本的 value state
      internalValue: this.getInitialValue(),
      // internalValue: initValue,
      propsValue: this.$props.value || {},
      maxFilterCount: FilterConfig.maxFilterCount,
      validating: false,
      log,
    }
  },
  computed: {
    // 处理 externalFirstAction 的不同类型
    processedExternalFirstAction() {
      if (!this.externalFirstAction) {
        return null
      }

      if (typeof this.externalFirstAction === 'object' && this.externalFirstAction !== null) {
        return this.externalFirstAction.firstAction || null
      }

      return this.externalFirstAction
    },

    externalFirstActionLength() {
      return this.externalFirstAction?.totalCount || 0
    },

    context() {
      const filterCount = this.internalValue.filters.map(v => v.filters?.length || 0).reduce((a, b) => a + b, 0)

      return {
        dataProvider: this.dataProvider,
        logProvider: Log,
        canAdd: filterCount < this.maxFilterCount,
        mode: this.mode,
        filterCount,
        validating: this.validating,
        isActionCollection: this.isActionCollection,
        externalFirstAction: this.processedExternalFirstAction, // 使用处理后的firstAction
        externalFirstActionLength: this.externalFirstActionLength,
        showPushData: this.showPushData, // 添加推送数据显示控制到context
        type: this.type,
      }
    },
  },
  watch: {
    // 监听 props.value 的变化，对应 React 版本的 useEffect 第一个依赖
    '$props.value': {
      handler(newValue) {
        // 仅当不相等时才刷新文档
        // todo 这里暂时注释
        // if (!_.isEmpty(newValue) && _.isEmpty(this.propsValue)) return;
        if (!_.isEqual(newValue, this.propsValue)) {
          this.internalValue = FilterModelUtil.fromJson(newValue)
        }
      },
      deep: true,
    },
    // 监听 propsValue 的变化，对应 React 版本的 useEffect 第二个依赖
    'propsValue': {
      handler(newValue, _oldValue) {
        // 当 propsValue 变化时，也需要检查是否需要同步
        if (!_.isEqual(this.$props.value, newValue)) {
          this.internalValue = FilterModelUtil.fromJson(this.$props.value)
        }
      },
      deep: true,
    },
  },
  mounted() {
  },
  methods: {
    getProcessedExternalFirstAction() {
      if (!this.externalFirstAction) {
        return null
      }

      // 如果是对象，提取 firstAction 属性
      if (typeof this.externalFirstAction === 'object' && this.externalFirstAction !== null) {
        return this.externalFirstAction.firstAction || null
      }

      // 如果是字符串，直接返回
      return this.externalFirstAction
    },

    getInitialValue() {
      const processedAction = this.getProcessedExternalFirstAction()
      const v = this.$props.value && this.$props.value.filters && this.$props.value.filters.length > 0
        ? FilterModelUtil.fromJson(this.$props.value, processedAction)
        : FilterModelUtil.initCreateFilterGroup(this.showInitLine, this.isActionCollection, processedAction)
      log.warn('初始化Filter时候的 this.externalFirstAction', this.externalFirstAction, '处理后的值:', processedAction)
      this.onValueChange(v)
      return v
    },

    addFilterGroup() {
      FilterModelUtil.addFilterGroupWithOneFilter(this.internalValue, this.isActionCollection, this.getProcessedExternalFirstAction())
      this.onValueChange(this.internalValue)
    },

    onValueChange(v) {
      const _v = FilterModelUtil.getValidJson(v)
      this.propsValue = _v
      this.onChange(_v, v)
      this.internalValue = { ...v }
    },

    // 暴露给父组件的方法
    isValid(flag) {
      // 设置验证状态
      this.validating = true
      this.context.validating = true

      // 直接使用FilterListGroup的递归校验结果，它会检查所有FilterSingle包括内部的BaseFilter
      const subFilterValid = this.$refs.filterGroupRef?.isValid?.(flag) ?? true

      // 验证完成后重置状态
      this.$nextTick(() => {
        this.validating = false
        this.context.validating = false
      })
      return subFilterValid
    },

    getFilterCount() {
      return this.internalValue.filters.map(v => v.filters?.length || 0).reduce((a, b) => a + b, 0)
    },
  },
}
</script>

<template>
  <div class="wolf-static-component_filter_FilterGroupPanel_event">
    <FilterContextProvider :value="context">
      <FilterListGroup ref="filterGroupRef" :value="internalValue" :on-change="onValueChange" />
      <div class="FilterAdder flex gap-6 items-center">
        <a-button
          type="dashed"
          :disabled="!context.canAdd"
          :style="{ display: context.mode === 'detail' ? 'none' : 'inline-block' }"
          @click="addFilterGroup"
        >
          <a-icon type="plus" />
          实时行为
        </a-button>
        <!-- <a-button
          type="dashed"
          @click="addFilterGroup"
          :disabled="!context.canAdd"
          :style="{ display: context.mode === 'detail' ? 'none' : 'inline-block' }"
        >
          <a-icon type="plus" />
          用户标签
        </a-button>
        <a-button
          type="dashed"
          @click="addFilterGroup"
          :disabled="!context.canAdd"
          :style="{ display: context.mode === 'detail' ? 'none' : 'inline-block' }"
        >
          <a-icon type="plus" />
          AI决策模型
        </a-button> -->
        <span
          :style="{
            marginLeft: '10px',
            display: context.mode === 'detail' ? 'none' : 'inline',
          }"
        >
          [{{ context.filterCount }}/{{ maxFilterCount }}] 最多添加{{ maxFilterCount }}条
        </span>
      </div>
    </FilterContextProvider>
  </div>
</template>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
