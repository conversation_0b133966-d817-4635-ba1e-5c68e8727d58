<script>
import { InputNumber, Select } from 'ant-design-vue'
import _ from 'lodash'
import { FilterConfig as FILTER_CONFIG } from 'wolf-static-cpnt/event/config'

const eventFirstValues = _.keys(FILTER_CONFIG.FIRST_ACTION)
const eventFirstNames = _.values(FILTER_CONFIG.FIRST_ACTION)
const timeUnitValues = _.keys(FILTER_CONFIG.TIME_TYPE)
const timeUnitNames = _.values(FILTER_CONFIG.TIME_TYPE)

export default {
  name: 'FilterFirstEventTime',
  components: {
    'a-select': Select,
    'a-select-option': Select.Option,
    'a-input-number': InputNumber,
  },
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      eventFirstValues,
      eventFirstNames,
      timeUnitValues,
      timeUnitNames,
    }
  },
  computed: {
    // 判断是否为"先做过, 后未做过"
    isFirstDoLastNotDo() {
      return this.value.isFirstDoLastNotDo()
    },
  },
  watch: {
    'value.firstAction': function (_newVal, _oldVal) {
      // 可以在这里添加需要的逻辑
    },
  },
  methods: {
    handleEventFirstChange(v) {
      let action
      if (v === 'DONE') {
        action = 'DONE'
      }
      else if (v === 'FIRST_DO_LAST_NOT_DO') {
        action = 'FIRST_DO'
      }
      else if (v === 'DO_SEQ') {
        action = 'DO_SEQ'
      }
      this.value.changeProperty({
        ...this.value,
        firstAction: v,
        action,
        // 保留firstTimeValue和firstTimeUnit，不要清空
        firstTimeValue: this.value.firstTimeValue || 1,
        firstTimeUnit: this.value.firstTimeUnit || 'HOUR',
        lastAction: v === 'FIRST_DO_LAST_NOT_DO' ? 'NOT_DO' : undefined,
      })
      this.onChange(this.value)
    },

    handleTimeValueChange(v) {
      this.value.changeProperty({
        ...this.value,
        firstTimeValue: v,
      })
      this.onChange(this.value)
    },

    handleTimeUnitChange(v) {
      this.value.changeProperty({
        ...this.value,
        firstTimeUnit: v,
      })
      this.onChange(this.value)
    },
  },
}
</script>

<template>
  <span style="display: flex; align-items: center; gap: 8px">
    <!-- 行为选择框 -->
    <a-select :value="value.firstAction" style="width: 150px" placeholder="行为" @change="handleEventFirstChange">
      <a-select-option v-for="(item, index) in eventFirstValues" :key="item" :value="item">
        {{ eventFirstNames[index] }}
      </a-select-option>
    </a-select>

    <span v-if="!isFirstDoLastNotDo" :style="{ display: 'flex', alignItems: 'center', gap: '8px' }">
      <span>在</span>

      <!-- 时间数值输入框 -->
      <a-input-number
        :value="value.firstTimeValue"
        :min="1"
        :max="999"
        style="width: 80px"
        placeholder="时间"
        @change="handleTimeValueChange"
      />

      <!-- 时间单位选择框 -->
      <a-select :value="value.firstTimeUnit" style="width: 80px" placeholder="时间单位" @change="handleTimeUnitChange">
        <a-select-option v-for="(item, index) in timeUnitValues" :key="item" :value="item">
          {{ timeUnitNames[index] }}
        </a-select-option>
      </a-select>

      <span>之内</span>
    </span>
  </span>
</template>

<style scoped>
/* 样式将从 event.scss 中继承 */
</style>
