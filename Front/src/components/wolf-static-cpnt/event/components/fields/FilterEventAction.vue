<script>
import _ from 'lodash'
import { FilterConfig as FILTER_CONFIG } from 'wolf-static-cpnt/event/config'

const optionValues = _.keys(FILTER_CONFIG.EVENT_ACTION)
const optionNames = _.values(FILTER_CONFIG.EVENT_ACTION)

export default {
  name: 'FilterEventAction',
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      optionValues,
      optionNames,
      FILTER_CONFIG,
    }
  },
  computed: {
    currentValue() {
      return this.type === 'last' ? this.value.lastAction : this.value.action
    },
  },
  // methods: {
  //   handleChange(v) {
  //     if (this.type === 'last') {
  //       this.value.changeProperty({ ...this.value, lastAction: v })
  //     }
  //     else {
  //       this.value.changeProperty({ ...this.value, action: v })
  //     }
  //     this.onChange(this.value)
  //   },
  // },
}
</script>

<template>
  <div class="actionStyle">
    {{ FILTER_CONFIG.EVENT_ACTION[currentValue] }}
  </div>
  <!-- <a-select :value="currentValue" style="width: 100%" placeholder="行为" @change="handleChange">
    <a-select-option v-for="(item, index) in optionValues" :key="item" :value="item">
      {{ optionNames[index] }}
    </a-select-option>
  </a-select> -->
</template>

<style scoped>
.actionStyle{
  width: 58px;
  height: 32px;
  /* line-height: 32px; */
  padding: 0 4px;
  font-size: 12px;
  background: #E0E7FF;
  color: #6366F1;
  border-radius: 4px;
  /* text-align: center; */
  /* align-items: center; */
}
</style>
