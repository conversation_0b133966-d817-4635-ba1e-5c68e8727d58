<script>
export default {
  name: 'FilterValue',
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      selectOptions: [],
    }
  },
  computed: {
    operator() {
      return this.value.eventAggregateProperty?.operator
    },
    fieldType() {
      const { eventAggregateProperty } = this.value
      if (eventAggregateProperty?.propertyType === 'TIMES') {
        return 'INT'
      }
      return eventAggregateProperty?.property?.fieldType || 'STRING'
    },
    isSimpleInput() {
      return (
        [
          'EQ',
          'NE',
          'GT',
          'GTE',
          'LT',
          'LTE',
          'LIKE',
          'NOT_LIKE',
          'START_WITH',
          'NOT_START_WITH',
          'END_WITH',
          'NOT_END_WITH',
        ].includes(this.operator) && ['STRING'].includes(this.fieldType)
      )
    },
    isNumberInput() {
      return (
        ['EQ', 'NE', 'GT', 'GTE', 'LT', 'LTE'].includes(this.operator)
        && ['INT', 'LONG', 'DOUBLE'].includes(this.fieldType)
      )
    },
    isBetweenInput() {
      return this.operator === 'BETWEEN'
    },
    isSelectInput() {
      return ['IN', 'NOT_IN'].includes(this.operator)
    },
  },
  methods: {
    getPlaceholder() {
      switch (this.fieldType) {
        case 'INT':
        case 'LONG':
          return '请输入整数'
        case 'DOUBLE':
          return '请输入数字'
        case 'STRING':
          return '请输入文本'
        default:
          return '请输入值'
      }
    },

    handleInputChange(e) {
      this.value.changeValue(e.target.value)
      this.onChange(this.value)
    },

    handleNumberChange(v) {
      this.value.changeValue(v)
      this.onChange(this.value)
    },

    handleBetweenChange(v, index) {
      const currentValue = this.value.eventAggregateProperty?.value || [null, null]
      currentValue[index] = v
      this.value.changeValue(currentValue)
      this.onChange(this.value)
    },

    handleSelectChange(v) {
      this.value.changeValue(v)
      this.onChange(this.value)
    },
  },
}
</script>

<template>
  <div class="FilterValue">
    <a-input
      v-if="isSimpleInput"
      :value="value.eventAggregateProperty?.value"
      :placeholder="getPlaceholder()"
      @change="handleInputChange"
    />
    <a-input-number
      v-else-if="isNumberInput"
      :min="1"
      :value="value.eventAggregateProperty?.value"
      style="width: 100%"
      :placeholder="getPlaceholder()"
      @change="handleNumberChange"
    />
    <div v-else-if="isBetweenInput" class="between-input">
      <a-input-number
        :min="1"
        :value="value.eventAggregateProperty?.value?.[0]"
        style="width: 45%"
        placeholder="最小值"
        @change="(v) => handleBetweenChange(v, 0)"
      />
      <span style="margin: 0 8px">-</span>
      <a-input-number
        :value="value.eventAggregateProperty?.value?.[1]"
        style="width: 45%"
        placeholder="最大值"
        @change="(v) => handleBetweenChange(v, 1)"
      />
    </div>
    <a-select
      v-else-if="isSelectInput"
      mode="multiple"
      :value="value.eventAggregateProperty?.value"
      style="width: 100%"
      :placeholder="getPlaceholder()"
      @change="handleSelectChange"
    >
      <a-select-option v-for="item in selectOptions" :key="item.value" :value="item.value">
        {{ item.label }}
      </a-select-option>
    </a-select>
    <!-- fixme: 暂时隐藏 -->
    <!-- <span v-else>-</span> -->
  </div>
</template>

<style scoped>
.FilterValue {
  /* fixme 暂时写死80 */
  max-width: 80px;
  display: block;
}

.between-input {
  display: flex;
  align-items: center;
  width: 100%;
}
</style>
