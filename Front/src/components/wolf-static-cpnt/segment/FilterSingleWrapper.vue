<script>
import { FilterSingleWrapperContextProvider } from './FilterSingleWrapperContext'

export default {
  name: 'FilterSingleWrapper',
  components: {
    FilterSingleWrapperContextProvider,
  },
  inject: ['filterContext'],
  props: {
    value: {
      type: [String, Number],
      default: '',
    },
    useTakePlaceWidth: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      style: {},
    }
  },
  computed: {
    context() {
      return this.filterContext()
    },
    mode() {
      return this.context.mode || 'edit'
    },
    displayValue() {
      return this.value || ''
    },
  },
  watch: {
    value: {
      handler() {
        this.updateStyle()
      },
    },
    mode: {
      handler() {
        this.updateStyle()
      },
    },
  },
  mounted() {
    this.updateStyle()
  },
  methods: {
    updateStyle() {
      this.$nextTick(() => {
        if (this.$refs.takePlace && this.mode === 'edit' && this.useTakePlaceWidth) {
          this.style = {
            width: `${Math.max(this.$refs.takePlace.clientWidth, 55) + 80}px`,
          }
        }
        else {
          this.style = {}
        }
      })
    },
  },
}
</script>

<template>
  <FilterSingleWrapperContextProvider :value="{}">
    <div class="FilterSingleWrapper" :style="style">
      <div v-if="mode === 'detail'" class="valueShow">
        {{ displayValue }}
      </div>
      <div v-if="mode === 'edit'" ref="takePlace" class="takePlace">
        {{ displayValue }}
      </div>
      <div v-if="mode === 'edit'">
        <slot />
      </div>
    </div>
  </FilterSingleWrapperContextProvider>
</template>

<style scoped>
/* 样式将从segment.scss中继承 */
</style>
