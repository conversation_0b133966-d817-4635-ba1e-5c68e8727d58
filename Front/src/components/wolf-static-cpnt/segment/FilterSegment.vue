<script>
import _ from 'lodash'
import UserGroupInfo from './UserGroupInfo.vue'

// 计算状态
export const calcStatusList = [
  {
    name: '未开始',
    text: '未开始',
    key: 'NOTRUN',
    value: 'NOTRUN',
  },
  {
    name: '计算中',
    text: '计算中',
    value: 'CALCING',
    key: 'CALCING',
  },
  {
    name: '计算失败',
    text: '计算失败',
    value: 'FAIL',
    key: 'FAIL',
  },
  {
    name: '计算成功',
    text: '计算成功',
    value: 'SUC',
    key: 'SUC',
  },
]

export default {
  name: 'FilterSegment',
  components: {
    UserGroupInfo,
  },
  inject: ['filterContext'],
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      _,
      fetching: true,
      searchValue: this.value?.segment?.name || '',
      dropDownOpen: false,
      segmentList: [],
      groupList: [],
      debounceTimer: null,
    }
  },
  computed: {
    context() {
      return this.filterContext()
    },
    dataProvider() {
      return this.context.dataProvider
    },
  },
  watch: {
    'value.segment': {
      handler(newValue) {
        // 当 segment 为 null 或 undefined 时，清空搜索值
        if (!newValue || !newValue.name) {
          this.searchValue = ''
        }
        else {
          this.searchValue = newValue.name || ''
        }
      },
      deep: true,
    },
    'searchValue': {
      handler(newValue) {
        // 实现防抖
        if (this.debounceTimer) {
          clearTimeout(this.debounceTimer)
        }
        this.debounceTimer = setTimeout(() => {
          if (this.dropDownOpen) {
            this.init(newValue)
          }
        }, 300)
      },
    },
  },

  async mounted() {
    await this.init(this.searchValue)
  },
  methods: {
    async init(searchText) {
      this.fetching = true
      try {
        const list = await this.dataProvider.getSegmentList(searchText)
        const res = await this.dataProvider.getGroupList()
        this.groupList = res
        this.segmentList = list
      }
      catch (error) {
        console.error('Failed to fetch segment list:', error)
        this.segmentList = []
      }
      this.fetching = false
    },

    filterOption(inputValue, option) {
      if (option.componentOptions?.children?.[0]?.text?.indexOf(inputValue) >= 0) {
        return true
      }
      return false
    },

    handleSearch(value) {
      this.searchValue = value
    },

    setDropDownOpen(open) {
      this.dropDownOpen = open
      if (open) {
        this.init(this.searchValue)
      }
    },

    onSegmentFilterChange(v) {
      const currentEvent = _.find(this.segmentList, item => item.id === v)

      // 清除选择时，重置搜索值并重新加载所有数据
      if (!v) {
        this.searchValue = ''
        this.init('')
      }

      this.value.changeProperty({
        ...this.value,
        segment: !_.isEmpty(currentEvent)
          ? {
              id: currentEvent.id,
              name: currentEvent.name,
              lastCalcTime: currentEvent.lastCalcTime,
              customerCount: currentEvent.customerCount,
              valid: currentEvent.valid,
            }
          : null, // 改为 null 而不是空对象
      })
      this.onChange(this.value)
    },
  },
}
</script>

<template>
  <div>
    <a-select
      show-search
      style="width: 100%"
      placeholder="请选择AI决策模型"
      :not-found-content="fetching ? undefined : null"
      :filter-option="filterOption"
      :value="value?.segment?.id"
      allow-clear
      option-label-prop="label"
      @search="handleSearch"
      @change="onSegmentFilterChange"
      @dropdown-visible-change="setDropDownOpen"
    >
      <template #notFoundContent>
        <a-spin v-if="fetching" size="small" />
      </template>
      <a-select-option v-for="segment in segmentList" :key="segment.id" :value="segment.id" :label="segment.name">
        <a-popover :key="segment.name" placement="leftTop" overlay-class-name="sementInfoItemPopover" trigger="hover">
          <template #content>
            <UserGroupInfo :data="segment" />
          </template>
          <div>{{ segment.name }}</div>
        </a-popover>
      </a-select-option>
    </a-select>
    <!-- fixme 这里正常是有过滤组件的 -->
  </div>
</template>

<style scoped>
/* 样式将从segment.scss中继承 */
</style>
