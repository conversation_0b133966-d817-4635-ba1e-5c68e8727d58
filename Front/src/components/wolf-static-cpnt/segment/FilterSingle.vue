<script>
import { Tooltip } from 'ant-design-vue'
import _ from 'lodash'
import FILTER_CONFIG from './FilterConfig'
import FilterSegment from './FilterSegment.vue'
import FilterSingleWrapper from './FilterSingleWrapper.vue'
import FilterType from './FilterType.vue'

export default {
  name: 'FilterSingle',
  components: {
    'a-tooltip': Tooltip,
    FilterSingleWrapper,
    FilterSegment,
    FilterType,
  },
  inject: ['filterContext'],
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    onDelete: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      validator: {},
      FILTER_CONFIG,
      _,
    }
  },
  computed: {
    context() {
      return this.filterContext()
    },
    mode() {
      return this.context.mode || 'edit'
    },
    type() {
      return this.value.type
    },
    segment() {
      return this.value.segment || {}
    },
  },
  watch: {
    'value.type': {
      handler() {
        this.updateValidator()
      },
    },
    'segment.id': {
      handler() {
        this.updateValidator()
      },
    },
  },
  mounted() {
    this.updateValidator()
  },
  methods: {
    updateValidator() {
      if (this.mode !== 'edit')
        return
      // 退出编辑
      this.validator = this.value.valid()
    },
  },
}
</script>

<template>
  <li :class="`FilterSingle ${mode}`">
    <div style="display: flex; align-items: center" :hidden="mode !== 'edit' && !value.valid().isValid">
      <div :class="`FilterField ${mode} ${validator?.type && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="FILTER_CONFIG.TYPE[type]" :use-take-place-width="true">
          <FilterType :value="value" :on-change="onChange" />
        </FilterSingleWrapper>
      </div>
      <div :class="`FilterField ${mode} ${validator?.id && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="segment?.name || ''" :use-take-place-width="true">
          <FilterSegment :value="value" :on-change="onChange" />
        </FilterSingleWrapper>
      </div>
      <div v-if="mode === 'edit'" class="Ctroller">
        <a-tooltip
          v-if="value.validating && (validator?.id || validator?.type)"
          placement="topRight"
          :title="_.head(_.values(validator.message))"
        >
          <div style="margin-right: 5px">
            <a-icon type="question-circle" class="Validator" />
          </div>
        </a-tooltip>
        <a-icon type="close-circle" class="delete" @click="onDelete" />
      </div>
      <div class="description">
        仅支持最新AI决策模型结果
      </div>
    </div>
  </li>
</template>

<style scoped>
/* 样式将从segment.scss中继承 */
</style>
