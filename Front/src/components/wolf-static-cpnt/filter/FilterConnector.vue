<script>
import { Switch } from 'ant-design-vue'
import FilterConfig from './FilterConfig'

export default {
  name: 'FilterConnector',
  components: {
    'a-switch': Switch,
  },
  inject: ['filterContext'],
  props: {
    value: {
      type: String,
      default: 'AND',
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  computed: {
    context() {
      return this.filterContext()
    },
    mode() {
      return this.context.mode || 'edit'
    },
    logProvider() {
      return (
        this.context.logProvider || {
          getLogger: () => ({ debug: console.log }),
        }
      )
    },
    FILTER_CONNECTOR() {
      return FilterConfig.connector
    },
    FILTER_CONNECTOR_SWITCH_MAP() {
      return {
        true: this.FILTER_CONNECTOR[0],
        false: this.FILTER_CONNECTOR[1],
      }
    },
    FILTER_CONNECTOR_SWITCH_MAP_REVERSE() {
      const map = {}
      map[this.FILTER_CONNECTOR[0].value] = true
      map[this.FILTER_CONNECTOR[1].value] = false
      return map
    },
  },
  mounted() {
    const log = this.logProvider.getLogger('FilterConnector')
    log.debug('value', this.value)
  },
  methods: {
    handleChange(v) {
      this.onChange(this.FILTER_CONNECTOR_SWITCH_MAP[v].value)
    },
  },
}
</script>

<template>
  <a-switch
    size="small"
    :checked-children="FILTER_CONNECTOR_SWITCH_MAP.true.name"
    :un-checked-children="FILTER_CONNECTOR_SWITCH_MAP.false.name"
    :checked="FILTER_CONNECTOR_SWITCH_MAP_REVERSE[value]"
    :disabled="mode === 'detail'"
    class="cpntSwitch"
    @change="handleChange"
  />
</template>

<style scoped>
/* 样式将从filter.scss中继承 */
</style>
