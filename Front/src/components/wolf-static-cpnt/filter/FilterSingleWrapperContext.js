// Vue 2 Context implementation for FilterSingleWrapper

export const FilterSingleWrapperContextProvider = {
  name: 'FilterSingleWrapperContextProvider',
  props: {
    value: {
      type: Object,
      default: () => ({ editing: false }),
    },
  },
  provide() {
    return {
      filterSingleWrapperContext: () => this.value,
    }
  },
  render() {
    return this.$slots.default
  },
}

export function useFilterSingleWrapperContext() {
  // This is a placeholder for Vue 2 compatibility
  return { editing: false }
}

export default {
  Provider: FilterSingleWrapperContextProvider,
  useContext: useFilterSingleWrapperContext,
}
