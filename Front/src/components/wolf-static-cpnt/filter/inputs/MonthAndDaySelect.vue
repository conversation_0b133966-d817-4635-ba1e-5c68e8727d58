<script>
import _ from 'lodash'

// 月份选项
const monthOption = Array.from({ length: 12 }, (_, i) => ({
  value: i + 1,
  label: `${i + 1}月`,
}))

// 日期选项
const dayOptions = Array.from({ length: 31 }, (_, i) => ({
  value: i + 1,
  label: `${i + 1}日`,
}))

export default {
  name: 'MonthAndDaySelect',
  props: {
    fieldValue: {
      type: [Array, Number],
      default: null,
    },
    operator: {
      type: String,
      required: true,
    },
    onChange: {
      type: Function,
      required: true,
    },
  },
  computed: {
    isBetween() {
      return this.operator === 'MONTH_BETWEEN' || this.operator === 'DAY_BETWEEN'
    },
    options() {
      return this.operator.includes('MONTH') ? monthOption : dayOptions
    },
    placeholder() {
      return this.operator.includes('MONTH') ? '月份' : '日期'
    },
    normalizedFieldValue() {
      let fieldValue = this.fieldValue
      if (this.isBetween && !_.isArray(fieldValue)) {
        fieldValue = []
      }
      return fieldValue
    },
  },
  methods: {
    handleFirstSelectChange(value) {
      if (!isNaN(value)) {
        this.onChange(this.isBetween ? [value, this.normalizedFieldValue[1]] : value)
      }
      else {
        this.onChange([undefined, this.normalizedFieldValue[1]])
      }
    },
    handleSecondSelectChange(value) {
      if (!isNaN(value)) {
        this.onChange([this.normalizedFieldValue[0], value])
      }
      else {
        this.onChange([this.normalizedFieldValue[0], undefined])
      }
    },
  },
}
</script>

<template>
  <div style="display: flex; gap: 4px">
    <a-select
      style="width: 100px"
      :options="options"
      :placeholder="placeholder"
      :value="isBetween ? normalizedFieldValue[0] : normalizedFieldValue"
      allow-clear
      show-search
      @change="handleFirstSelectChange"
    />
    <template v-if="isBetween">
      <span>~</span>
      <a-select
        style="width: 100px"
        :options="options"
        :placeholder="placeholder"
        :value="normalizedFieldValue[1]"
        allow-clear
        show-search
        @change="handleSecondSelectChange"
      />
    </template>
  </div>
</template>

<style scoped>
/* MonthAndDaySelect 特定样式 */
.month-day-selector {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
