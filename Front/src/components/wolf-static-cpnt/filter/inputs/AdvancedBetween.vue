<script>
import SelectTime from '@/components/wolf-static-cpnt/selectTime/index.vue'

export default {
  name: 'AdvancedBetween',
  components: {
    SelectTime,
  },
  props: {
    fieldValue: {
      type: [Array, String],
      default: null,
    },
    onChange: {
      type: Function,
      required: true,
    },
  },
}
</script>

<template>
  <SelectTime show-time :data="fieldValue" :on-change="onChange" />
</template>

<style scoped>
</style>
