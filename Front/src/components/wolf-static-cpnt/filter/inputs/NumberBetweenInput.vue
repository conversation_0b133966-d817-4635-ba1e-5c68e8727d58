<script>
import _ from 'lodash'

export default {
  name: 'NumberBetweenInput',
  props: {
    fieldValue: {
      type: Array,
      default: () => [],
    },
    onChange: {
      type: Function,
      required: true,
    },
  },
  computed: {
    normalizedFieldValue() {
      return _.isArray(this.fieldValue) ? this.fieldValue : []
    },
  },
  methods: {
    handleMinChange(value) {
      if (!isNaN(value)) {
        this.onChange([value, this.normalizedFieldValue[1]])
      }
    },
    handleMaxChange(value) {
      if (!isNaN(value)) {
        this.onChange([this.normalizedFieldValue[0], value])
      }
    },
  },
}
</script>

<template>
  <div>
    <a-input-number
      style="width: 100px; margin-right: 5px"
      placeholder="最小值"
      :value="normalizedFieldValue[0]"
      @change="handleMinChange"
    />
    至
    <a-input-number
      style="width: 100px; margin-left: 5px"
      placeholder="最大值"
      :value="normalizedFieldValue[1]"
      @change="handleMaxChange"
    />
  </div>
</template>

<style scoped>
/* NumberBetweenInput 特定样式 */
.number-range {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
