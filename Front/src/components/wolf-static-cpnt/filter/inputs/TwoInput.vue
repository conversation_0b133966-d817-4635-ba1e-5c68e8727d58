<script>
import DateBetweenInput from './DateBetweenInput.vue'
import NumberBetweenInput from './NumberBetweenInput.vue'

export default {
  name: 'TwoInput',
  components: {
    DateBetweenInput,
    NumberBetweenInput,
  },
  props: {
    fieldType: {
      type: String,
      required: true,
    },
    operator: {
      type: String,
      required: true,
    },
    fieldValue: {
      type: Array,
      default: () => [],
    },
    items: {
      type: Array,
      default: () => [],
    },
    onChange: {
      type: Function,
      required: true,
    },
  },
  computed: {
    isDateType() {
      return (
        this.fieldType === 'DATE'
        || this.fieldType === 'DATETIME'
        || this.fieldType === 'TIMESTAMP'
        || this.fieldType === 'HIVE_DATE'
        || this.fieldType === 'HIVE_TIMESTAMP'
      )
    },
    currentComponent() {
      return this.isDateType ? 'DateBetweenInput' : 'NumberBetweenInput'
    },
  },
}
</script>

<template>
  <component
    :is="currentComponent"
    :field-type="fieldType"
    :operator="operator"
    :field-value="fieldValue"
    :items="items"
    :on-change="onChange"
  />
</template>

<style scoped>
/* TwoInput 特定样式 */
</style>
