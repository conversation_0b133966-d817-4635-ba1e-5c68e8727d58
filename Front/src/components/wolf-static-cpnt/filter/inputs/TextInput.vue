<script>
import _ from 'lodash'

export default {
  name: 'TextInput',
  inject: ['filterValueContext'],
  props: {
    fieldValue: {
      type: [String, Number],
      default: '',
    },
    items: {
      type: Array,
      default: () => [],
    },
    onChange: {
      type: Function,
      required: true,
    },
  },
  computed: {
    filteredItems() {
      const items = _.isArray(this.items) ? this.items.filter(n => !!n.value) : []
      return items.map(v => ({
        value: v.value || v,
        text: v.value || v,
      }))
    },
  },
  mounted() {},
  methods: {
    handleInput(value) {
      this.onChange(value)
    },
    handleDropdownVisibleChange(visible) {
      this.filterValueContext().setMenuVisible(visible)
    },
    filterOption(inputValue, option) {
      return (
        `${option.value}`.toUpperCase().indexOf(typeof inputValue === 'string' ? inputValue.toUpperCase() : '') !== -1
      )
    },
  },
}
</script>

<template>
  <a-auto-complete
    style="width: 170px"
    :data-source="filteredItems"
    :show-search="items.length > 0"
    :value="fieldValue"
    :filter-option="filterOption"
    @change="handleInput"
    @dropdownVisibleChange="handleDropdownVisibleChange"
  />
</template>

<style scoped>
/* TextInput 特定样式 */
</style>
