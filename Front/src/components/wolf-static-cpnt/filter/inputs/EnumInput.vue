<script>
import _ from 'lodash'

export default {
  name: 'EnumInput',
  inject: ['filterValueContext'],
  props: {
    fieldValue: {
      type: Array,
      default: () => [],
    },
    items: {
      type: Array,
      default: () => [],
    },
    onChange: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      checked: false,
    }
  },
  computed: {
    normalizedFieldValue() {
      return _.isArray(this.fieldValue) ? this.fieldValue : []
    },
    filteredItems() {
      return this.items.filter(n => !!n.value)
    },
  },
  watch: {
    checked(newValue) {
      if (newValue && !_.isEmpty(this.fieldValue)) {
        this.changeSelect(this.fieldValue)
      }
    },
  },
  methods: {
    changeSelect(v) {
      if (this.checked) {
        const mapArr = [',', '，', ' ', '、', ';', '；']
        const regex = new RegExp(`[${mapArr.join('')}]`, 'g')

        let result = v.map(item => item.replace(regex, ',').split(',')).reduce((acc, curr) => acc.concat(curr), [])

        result = result.filter(item => item)
        result = _.uniq(result)
        this.onChange(result)
      }
      else {
        this.onChange(v)
      }
    },
    changeCheckBox(e) {
      if (!e.target.checked) {
        this.onChange([])
      }
      this.checked = e.target.checked
    },
    handleDropdownVisibleChange(visible) {
      this.filterValueContext().setMenuVisible(visible)
    },
  },
}
</script>

<template>
  <div style="display: flex; gap: 12px">
    <a-select
      mode="tags"
      allow-clear
      :max-tag-count="10"
      :value="normalizedFieldValue"
      style="min-width: 150px"
      option-label-prop="label"
      @change="changeSelect"
      @dropdownVisibleChange="handleDropdownVisibleChange"
    >
      <a-select-option
        v-for="(item, i) in filteredItems"
        :key="i"
        :value="item.value ? item.value : item"
        :label="item.name ? item.name : item"
      >
        {{ item.name ? item.name : item }}
      </a-select-option>
    </a-select>
    <a-checkbox
      :checked="checked"
      style="height: 30px; white-space: nowrap; display: flex; align-items: center"
      @change="changeCheckBox"
    >
      <a-tooltip
        :overlay-style="{ maxWidth: '350px' }"
        title="勾选后，输入内容使用逗号(中英文)、分号(中英文)、空格、顿号作为分隔符拆分多值"
      >
        使用分隔符输入
      </a-tooltip>
    </a-checkbox>
  </div>
</template>

<style scoped>
/* EnumInput 特定样式 */
.enum-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.enum-input-wrapper .ant-select {
  min-width: 150px;
}

.enum-input-wrapper .ant-checkbox-wrapper {
  white-space: nowrap;
}
</style>
