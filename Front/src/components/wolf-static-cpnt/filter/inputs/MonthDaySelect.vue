<script>
import _ from 'lodash'

// 月份选项
const monthOption = Array.from({ length: 12 }, (_, i) => ({
  value: i + 1,
  label: `${i + 1}月`,
}))

// 日期选项
const dayOptions = Array.from({ length: 31 }, (_, i) => ({
  value: i + 1,
  label: `${i + 1}日`,
}))

export default {
  name: 'MonthDaySelect',
  props: {
    fieldValue: {
      type: [Array, String],
      default: null,
    },
    operator: {
      type: String,
      required: true,
    },
    onChange: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      monthOption,
      dayOptions,
    }
  },
  computed: {
    isBetween() {
      return this.operator === 'DATE_FORMAT_BETWEEN'
    },
    isEQ() {
      return this.operator === 'DATE_FORMAT_EQ'
    },
    normalizedFieldValue() {
      let fieldValue = this.fieldValue
      if (this.isBetween && !_.isArray(fieldValue)) {
        fieldValue = []
      }
      return fieldValue
    },
    parsedValues() {
      const [start, end] = this.isBetween ? this.normalizedFieldValue : [this.normalizedFieldValue, undefined]
      const [startMonth, startDay] = this.splitValue(start)
      const [endMonth, endDay] = this.splitValue(end)
      return { startMonth, startDay, endMonth, endDay }
    },
  },
  methods: {
    splitValue(value) {
      return _.isString(value) ? value.split('-') : [undefined, undefined]
    },
    handleSelectChange(part, value, index) {
      const { startMonth, startDay, endMonth, endDay } = this.parsedValues
      let newStartMonth = startMonth || undefined
      let newStartDay = startDay || undefined
      let newEndMonth = endMonth || undefined
      let newEndDay = endDay || undefined

      if (index === 0)
        newStartMonth = value
      else if (index === 1)
        newStartDay = value
      else if (index === 2)
        newEndMonth = value
      else if (index === 3)
        newEndDay = value

      if (this.isBetween) {
        this.onChange([`${newStartMonth}-${newStartDay}`, `${newEndMonth}-${newEndDay}`])
      }
      else if (this.isEQ) {
        this.onChange(`${newStartMonth}-${newStartDay}`)
      }
    },
    getSelectValue(index) {
      const { startMonth, startDay, endMonth, endDay } = this.parsedValues
      let value = ''
      if (index === 0)
        value = startMonth
      else if (index === 1)
        value = startDay
      else if (index === 2)
        value = endMonth
      else if (index === 3)
        value = endDay

      return value ? (value.includes('undefined') ? undefined : value) : value
    },
  },
}
</script>

<template>
  <div style="display: flex; gap: 4px">
    <a-select
      style="width: 100px"
      :options="monthOption"
      placeholder="月份"
      :value="getSelectValue(0)"
      allow-clear
      show-search
      @change="(v) => handleSelectChange('month', v, 0)"
    />
    <a-select
      style="width: 100px"
      :options="dayOptions"
      placeholder="日期"
      :value="getSelectValue(1)"
      allow-clear
      show-search
      @change="(v) => handleSelectChange('day', v, 1)"
    />
    <template v-if="isBetween">
      <span>~</span>
      <a-select
        style="width: 100px"
        :options="monthOption"
        placeholder="月份"
        :value="getSelectValue(2)"
        allow-clear
        show-search
        @change="(v) => handleSelectChange('month', v, 2)"
      />
      <a-select
        style="width: 100px"
        :options="dayOptions"
        placeholder="日期"
        :value="getSelectValue(3)"
        allow-clear
        show-search
        @change="(v) => handleSelectChange('day', v, 3)"
      />
    </template>
  </div>
</template>

<style scoped>
/* MonthDaySelect 特定样式 */
</style>
