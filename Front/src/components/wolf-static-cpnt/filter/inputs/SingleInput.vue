<script>
import DateInput from './DateInput.vue'
import TextInput from './TextInput.vue'

export default {
  name: 'SingleInput',
  components: {
    TextInput,
    DateInput,
  },
  props: {
    fieldType: {
      type: String,
      required: true,
    },
    fieldValue: {
      type: [String, Number],
      default: '',
    },
    items: {
      type: Array,
      default: () => [],
    },
    onChange: {
      type: Function,
      required: true,
    },
  },
  computed: {
    isDateType() {
      return (
        this.fieldType === 'DATE'
        || this.fieldType === 'DATETIME'
        || this.fieldType === 'TIMESTAMP'
        || this.fieldType === 'HIVE_DATE'
        || this.fieldType === 'HIVE_TIMESTAMP'
      )
    },
    currentComponent() {
      return this.isDateType ? 'DateInput' : 'TextInput'
    },
  },
  mounted() {},
}
</script>

<template>
  <component
    :is="currentComponent"
    :field-type="fieldType"
    :field-value="fieldValue"
    :items="items"
    :on-change="onChange"
  />
</template>

<style scoped>
/* SingleInput 特定样式 */
</style>
