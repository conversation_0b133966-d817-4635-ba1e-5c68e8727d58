<script>
import _ from 'lodash'
import Log from '../../utils/log'

export default {
  name: 'FilterField',
  inject: ['filterContext'],
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      propertyMap: {},
      searchText: this.value.field || '',
      menuVisible: false,
      fetching: false,
      debounceSearchText: '',
      _debounceTimer: null,
      log: Log.getLogger('FilterField'),
    }
  },
  computed: {
    context() {
      return this.filterContext()
    },
    dataProvider() {
      return this.context.dataProvider || {}
    },
    logProvider() {
      return this.context.logProvider || Log
    },
    isPropertyMapEmpty() {
      return _.isEmpty(this.propertyMap)
    },
  },
  watch: {
    'value.fieldName': {
      handler(newValue) {
        if (this.menuVisible) {
          this.searchText = ''
        }
        else {
          this.searchText = newValue || ''
        }
      },
      immediate: true,
    },
    menuVisible(newValue) {
      if (newValue) {
        this.searchText = ''
      }
      else {
        this.searchText = this.value.fieldName || ''
      }
    },
    searchText(val) {
      if (this._debounceTimer)
        clearTimeout(this._debounceTimer)
      this._debounceTimer = setTimeout(() => {
        this.debounceSearchText = val
      }, 200)
    },
    debounceSearchText() {
      this.handleDebounceSearch()
    },
  },
  mounted() {
    // 组件挂载时，如果有dataProvider，预加载一次数据
    if (this.dataProvider && this.dataProvider.getPropertyList) {
      this.fetchPropertyList(this.dataProvider)('')
    }
  },
  methods: {
    getPopupContainer(triggerNode) {
      return triggerNode.parentNode
    },

    handleVisibleChange(visible) {
      this.menuVisible = visible
      // 当菜单打开时，如果没有数据，主动获取一次
      if (visible && this.isPropertyMapEmpty && !this.fetching) {
        console.log('🚀 ~ handleVisibleChange ~ 主动获取数据')
        this.handleDebounceSearch()
      }
    },

    handleSearchInput(e) {
      this.searchText = e.target.value
    },

    handleFocus(event) {
      event.target.select()
    },

    async handleDebounceSearch() {
      if (!this.menuVisible)
        return
      this.fetching = true

      // 如果搜索文本和当前字段名相同，且已有数据，则不重复获取
      if (this.debounceSearchText === this.value.fieldName && !this.isPropertyMapEmpty) {
        this.fetching = false
        return
      }

      try {
        await this.fetchPropertyList(this.dataProvider)(this.debounceSearchText)
      }
      catch (error) {
        this.log.error('Failed to fetch property list:', error)
        this.fetching = false
      }
    },

    fetchPropertyList(dp) {
      return async (name) => {
        this.log.debug('fetchPropertyList')
        const plist = await dp.getPropertyList(name, dp.eventId)
        const levelMap = _.groupBy(plist, v => v.level1)
        _.keys(levelMap).forEach((level1) => {
          levelMap[level1] = _.groupBy(levelMap[level1], v => v.level2)
        })
        this.fetching = false
        this.propertyMap = levelMap
        return levelMap
      }
    },

    getFilteredProperties(properties) {
      // 如果没有搜索文本或搜索文本为空，返回所有属性
      if (!this.searchText || this.searchText.trim() === '') {
        return properties || []
      }

      // 确保properties是数组
      if (!Array.isArray(properties)) {
        return []
      }

      return properties.filter(
        v =>
          (v.field && v.field.indexOf(this.searchText) >= 0)
          || (v.fieldName && v.fieldName.indexOf(this.searchText) >= 0),
      )
    },

    getPropertyDisplayType(properties, property) {
      const isSession = properties.some(item => item.isSession)
      return isSession ? property.dataDisplayType || property.fieldType : property.field
    },

    isObject(obj) {
      return _.isObject(obj) && !_.isArray(obj)
    },

    onSelectProperty(property) {
      this.value.changeProperty(property)
      this.onChange && this.onChange(this.value)
      this.menuVisible = false
    },
  },
}
</script>

<template>
  <div class="FilterField">
    <a-dropdown
      :trigger="['click']"
      :visible="menuVisible"
      :get-popup-container="getPopupContainer"
      @visibleChange="handleVisibleChange"
    >
      <div class="clickWrapper">
        <a-input
          class="ant-dropdown-link"
          placeholder="属性"
          :value="searchText"
          @input="handleSearchInput"
          @focus="handleFocus"
        />
        <a-icon type="down" style="color: rgba(0, 0, 0, 0.45); font-size: 12px" />
      </div>

      <a-menu
        slot="overlay"
        :style="{
          maxHeight: '400px',
          overflowY: 'auto',
          maxWidth: '500px',
          overflowX: 'auto',
        }"
      >
        <a-menu-item v-if="fetching && isPropertyMapEmpty" key="loading">
          <a-icon type="sync" :spin="true" />
          loading...
        </a-menu-item>
        <template v-else>
          <template v-for="(children, level1) in propertyMap">
            <template v-if="!level1">
              <template v-if="Array.isArray(children)">
                <a-menu-item
                  v-for="(property, i) in getFilteredProperties(children)"
                  :key="`${property.level1}_${property.level2}_${property.fieldName}_${i}`"
                  @click="() => onSelectProperty(property)"
                >
                  {{ property.fieldName }}[{{ getPropertyDisplayType(children, property) }}]
                </a-menu-item>
              </template>
              <template v-else-if="isObject(children) && !Object.entries(children)[0][0]">
                <a-menu-item
                  v-for="(property, i) in getFilteredProperties(Object.entries(children)[0][1])"
                  :key="`${property.level1}_${property.level2}_${property.fieldName}_${i}`"
                  @click="() => onSelectProperty(property)"
                >
                  {{ property.fieldName }}[{{ getPropertyDisplayType(Object.entries(children)[0][1], property) }}]
                </a-menu-item>
              </template>
            </template>
            <a-menu-item-group v-else :key="level1" :title="level1">
              <template v-for="(level2Children, level2) in children">
                <template v-if="!level2">
                  <a-menu-item
                    v-for="(property, i) in getFilteredProperties(level2Children)"
                    :key="`${property.level1}_${property.level2}_${property.fieldName}_${i}`"
                    @click="() => onSelectProperty(property)"
                  >
                    {{ property.fieldName }}[{{ getPropertyDisplayType(level2Children, property) }}]
                  </a-menu-item>
                </template>
                <a-menu-item-group v-else :key="level2" :title="level2">
                  <a-menu-item
                    v-for="(property, i) in getFilteredProperties(level2Children)"
                    :key="`${property.level1}_${property.level2}_${property.fieldName}_${i}`"
                    @click="() => onSelectProperty(property)"
                  >
                    {{ property.fieldName }}[{{ getPropertyDisplayType(level2Children, property) }}]
                  </a-menu-item>
                </a-menu-item-group>
              </template>
            </a-menu-item-group>
          </template>
        </template>
      </a-menu>
    </a-dropdown>
  </div>
</template>

<style scoped>
.FilterField {
  display: inline-block;
  width: 100%;
}

.clickWrapper {
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.clickWrapper .ant-input {
  padding-right: 20px;
}

.clickWrapper .anticon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.ant-dropdown-link {
  cursor: pointer;
}
</style>
