<script>
import { PlusOutlined } from '@ant-design/icons-vue'
import { Button, Popover, Select } from 'ant-design-vue'
import moment from 'moment'
import Log from '../../utils/log'
import ComplexGroup from './ComplexGroup.vue'
import ComplexModelUtil from './ComplexModelUtil'
import FilterConfig from './FilterConfig'
import { FilterContextProvider } from './FilterContext'
import FilterListGroup from './FilterListGroup.vue'

const log = Log.getLogger('Complex')

export default {
  name: 'Complex',
  components: {
    'a-button': Button,
    'a-select': Select,
    'a-select-option': Select.Option,
    'a-popover': Popover,
    'plus-outlined': PlusOutlined,
    FilterContextProvider,
    ComplexGroup,
    FilterListGroup,
  },
  props: {
    value: {
      type: Object,
      default: () => ComplexModelUtil.initCreateFilterGroup(),
    },
    dataProvider: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    mode: {
      type: String,
      default: 'edit',
    },
    selectList: {
      type: Array,
      default: () => [],
    },
    isUserGroup: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const { maxFilterCount } = FilterConfig
    return {
      maxFilterCount,
      currentValue: ComplexModelUtil.fromJson(this.value),
      propsValue: {},
      segmentList: [],
    }
  },
  computed: {
    context() {
      return {
        dataProvider: this.dataProvider,
        logProvider: Log,
        mode: this.mode,
        validating: false,
        isUserGroup: this.isUserGroup,
        canAdd: this.filterCount < this.maxFilterCount,
        filterCount: this.filterCount,
      }
    },
    filterCount() {
      return this.currentValue.filters ? this.currentValue.filters.length : 0
    },
  },
  watch: {
    value: {
      handler(newValue) {
        if (newValue !== this.propsValue) {
          this.currentValue = ComplexModelUtil.fromJson(newValue)
        }
      },
      deep: true,
    },
    selectList: {
      handler() {
        this.updateSegmentList()
      },
      immediate: true,
    },
  },
  mounted() {
    log.debug('Complex mounted', this.value)
    this.updateSegmentList()
  },
  methods: {
    updateSegmentList() {
      const list = [...this.selectList]
      if (this.value.filters) {
        this.value.filters.forEach((n) => {
          if (n.id && list.findIndex(k => k.id === n.id) === -1) {
            const info = { ...n }
            delete info.filter
            list.push(info)
          }
        })
      }
      this.segmentList = list
    },

    addComplexGroup() {
      ComplexModelUtil.addComplexGroupWithOneFilter(this.currentValue)
      const _value = ComplexModelUtil.getValidJson(this.currentValue)
      this.propsValue = _value
      this.onChange(_value)
    },

    onValueChange(v, index) {
      log.debug('onValueChanged', JSON.stringify(v))
      this.currentValue.filters[index].filter = v
      const _value = ComplexModelUtil.getValidJson(this.currentValue)
      this.propsValue = _value
      this.onChange(_value)
    },

    onChangeConnector(filter) {
      return (v) => {
        filter.connector = v
        this.onChange(this.currentValue)
      }
    },

    onChangedd(id, index) {
      const info = this.selectList.find(n => n.id === id)
      if (info) {
        this.currentValue.filters[index].id = info.id
        this.currentValue.filters[index].name = info.name
        this.currentValue.filters[index].lastCalcTime = info.lastCalcTime
        this.currentValue.filters[index].customerCount = info.customerCount
        const _value = ComplexModelUtil.getValidJson(this.currentValue)
        this.propsValue = _value
        this.onChange(_value)
      }
    },

    filterOption(input, option) {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },

    formatTime(time) {
      return time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : '未知'
    },

    isValid() {
      this.context.validating = true
      return ComplexModelUtil.isFilterListGroupValid(this.currentValue)
    },
  },
}
</script>

<template>
  <div class="wolf-static-component_filter_FilterGroupPanel">
    <FilterContextProvider :value="context">
      <ComplexGroup
        :connector="value.connector"
        :on-change-connector="onChangeConnector(value)"
        :filter-count="filterCount"
        inner="inner"
        :mode="mode"
      >
        <div v-for="(filter, index) in value.filters" :key="index" style="margin-bottom: 20px">
          <div style="margin-left: 0px !important">
            <a-select
              v-model="filter.id"
              show-search
              :filter-option="filterOption"
              :disabled="mode === 'detail'"
              :class="context.validating && !filter.id ? 'has-error' : ''"
              option-label-prop="label"
              style="width: 280px"
              placeholder="请选择分群"
              @change="(id) => onChangedd(id, index)"
            >
              <a-select-option v-for="item in segmentList" :key="item.id" :value="item.id" :label="item.name">
                <a-popover placement="right" overlay-class-name="conplexInfoItemPopover" trigger="click">
                  <template #content>
                    <div class="renderUserGroupInfo">
                      <div class="title">
                        {{ item.name }}
                      </div>
                      <div class="count">
                        {{ item.customerCount || 0 }}
                      </div>
                      <div class="lastCalcTime">
                        最后计算时间: {{ formatTime(item.lastCalcTime) }}
                      </div>
                    </div>
                  </template>
                  <div style="width: 280px">
                    {{ item.name }}
                  </div>
                </a-popover>
              </a-select-option>
            </a-select>
          </div>

          <div v-if="filter.id" style="margin-left: 20px">
            <FilterListGroup :value="filter.filter" @change="(val) => onValueChange(val, index)" />
          </div>
        </div>
      </ComplexGroup>

      <div v-if="mode !== 'detail'" class="FilterAdder">
        <a-button type="dashed" :disabled="!context.canAdd" @click="addComplexGroup">
          <plus-outlined />
          添加AI决策模型
        </a-button>
        <span style="margin-left: 10px"> [{{ filterCount }}/{{ maxFilterCount }}] 最多添加{{ maxFilterCount }}条 </span>
      </div>
    </FilterContextProvider>
  </div>
</template>

<style lang="scss" scoped>
.FilterAdder {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

.has-error {
  :deep(.ant-select-selector) {
    border-color: #ff4d4f !important;
  }
}
</style>
