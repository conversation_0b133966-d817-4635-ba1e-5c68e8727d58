<script>
import _ from 'lodash'
import { FilterSingleWrapperContextProvider } from './FilterSingleWrapperContext'

export default {
  name: 'FilterSingleWrapper',
  components: {
    FilterSingleWrapperContextProvider,
  },
  inject: ['filterContext'],
  props: {
    value: {
      type: [String, Number, Boolean, Array],
      default: '',
    },
    useTakePlaceWidth: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      wrapperStyle: {},
    }
  },
  computed: {
    context() {
      return this.filterContext()
    },
    mode() {
      return this.context.mode || 'edit'
    },
    displayValue() {
      if (!this.value) {
        return ''
      }
      return _.isArray(this.value) ? `[${this.value.join(',')}]` : this.value
    },
  },
  watch: {
    value() {
      this.updateStyle()
    },
    mode() {
      this.updateStyle()
    },
    useTakePlaceWidth() {
      this.updateStyle()
    },
  },
  mounted() {
    this.updateStyle()
  },
  methods: {
    updateStyle() {
      this.$nextTick(() => {
        if (this.$refs.takePlace && this.mode === 'edit' && this.useTakePlaceWidth) {
          this.wrapperStyle = {
            width: `${Math.max(this.$refs.takePlace.clientWidth, 50) + 45}px`,
          }
        }
        else {
          this.wrapperStyle = {}
        }
      })
    },
  },
}
</script>

<template>
  <FilterSingleWrapperContextProvider :value="{}">
    <div class="FilterSingleWrapper" :style="wrapperStyle">
      <div v-if="mode === 'detail'" class="valueShow">
        {{ displayValue }}
      </div>
      <div v-if="mode === 'edit'" ref="takePlace" class="takePlace">
        {{ displayValue }}
      </div>
      <div v-if="mode === 'edit'">
        <slot />
      </div>
    </div>
  </FilterSingleWrapperContextProvider>
</template>

<style scoped>
/* 样式将从 filter.scss 中继承 */
</style>
