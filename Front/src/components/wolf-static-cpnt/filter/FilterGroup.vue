<script>
import FilterConnector from './FilterConnector.vue'

export default {
  name: 'FilterGroup',
  components: {
    FilterConnector,
  },
  inject: ['filterContext'],
  props: {
    connector: {
      type: String,
      default: 'AND',
    },
    onChangeConnector: {
      type: Function,
      default: () => {},
    },
    filterCount: {
      type: Number,
      default: 0,
    },
    inner: {
      type: String,
      default: '',
    },
  },
  computed: {
    context() {
      return this.filterContext()
    },
    logProvider() {
      return (
        this.context.logProvider || {
          getLogger: () => ({ debug: console.log }),
        }
      )
    },
  },
  mounted() {
    const log = this.logProvider.getLogger('FilterGroup')
    log.debug('connector', this.connector)
  },
}
</script>

<template>
  <div class="FilterGroupPanel">
    <div class="ConnectorPanel" :style="{ display: filterCount <= 1 ? 'none' : 'block' }">
      <div class="TopLine" />
      <div class="VLine" />
      <div class="BottomLine" />
      <div class="Connector">
        <FilterConnector :value="connector" :on-change="onChangeConnector" />
      </div>
    </div>
    <ul :class="`FilterList ${inner}`">
      <slot />
    </ul>
  </div>
</template>

<style lang="scss">
@import "./filter.scss";
</style>
