const timeOperator = ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN']
const userGroupTimeOperator = [
  'EQ',
  'NE',
  'IS_NOT_NULL',
  'IS_NULL',
  'GT',
  'GTE',
  'LT',
  'LTE',
  'BETWEEN',
  'ADVANCED_BETWEEN',
  'TIMESLICING',
  'MONTH_BETWEEN',
  'DAY_BETWEEN',
  'DATE_FORMAT_BETWEEN',
  'MONTH_EQ',
  'DAY_EQ',
  'DATE_FORMAT_EQ',
]

export default {
  maxFilterCount: 20,
  operatorList: [
    { name: '等于', operator: 'EQ' },
    { name: '不等于', operator: 'NE' },
    { name: '大于', operator: 'GT' },
    { name: '大于等于', operator: 'GTE' },
    { name: '小于', operator: 'LT' },
    { name: '小于等于', operator: 'LTE' },
    { name: '范围', operator: 'BETWEE<PERSON>' },
    { name: '高级范围', operator: 'ADVANCED_BETWEEN' },
    { name: '包含', operator: 'IN' },
    { name: '不包含', operator: 'NOT_IN' },
    { name: '有值', operator: 'IS_NOT_NULL' },
    { name: '空值', operator: 'IS_NULL' },
    { name: '匹配', operator: 'LIKE' },
    { name: '不匹配', operator: 'NOT_LIKE' },
    { name: '开头匹配', operator: 'START_WITH' },
    { name: '开头不匹配', operator: 'NOT_START_WITH' },
    { name: '结尾匹配', operator: 'END_WITH' },
    { name: '结尾不匹配', operator: 'NOT_END_WITH' },
    { name: '是', operator: 'IS_TRUE' },
    { name: '否', operator: 'IS_FALSE' },
  ],
  userGroupOperatorList: [
    { name: '等于', operator: 'EQ' },
    { name: '不等于', operator: 'NE' },
    { name: '大于', operator: 'GT' },
    { name: '大于等于', operator: 'GTE' },
    { name: '小于', operator: 'LT' },
    { name: '小于等于', operator: 'LTE' },
    { name: '范围', operator: 'BETWEEN' },
    { name: '高级范围', operator: 'ADVANCED_BETWEEN' },
    { name: '包含', operator: 'IN' },
    { name: '不包含', operator: 'NOT_IN' },
    { name: '有值', operator: 'IS_NOT_NULL' },
    { name: '空值', operator: 'IS_NULL' },
    { name: '匹配', operator: 'LIKE' },
    { name: '不匹配', operator: 'NOT_LIKE' },
    { name: '开头匹配', operator: 'START_WITH' },
    { name: '开头不匹配', operator: 'NOT_START_WITH' },
    { name: '结尾匹配', operator: 'END_WITH' },
    { name: '结尾不匹配', operator: 'NOT_END_WITH' },
    { name: '是', operator: 'IS_TRUE' },
    { name: '否', operator: 'IS_FALSE' },
    {
      name: '时间切片',
      operator: 'TIMESLICING',
      children: [
        { name: '时间切片-月范围', operator: 'MONTH_BETWEEN' },
        { name: '时间切片-日范围', operator: 'DAY_BETWEEN' },
        { name: '时间切片-月日范围', operator: 'DATE_FORMAT_BETWEEN' },
        { name: '时间切片-月等于', operator: 'MONTH_EQ' },
        { name: '时间切片-日等于', operator: 'DAY_EQ' },
        { name: '时间切片-月日等于', operator: 'DATE_FORMAT_EQ' },
      ],
    },
  ],
  typeOperator: {
    INT: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    LONG: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    DOUBLE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    HIVE_DATE: timeOperator,
    DATE: timeOperator,
    DATETIME: timeOperator,
    HIVE_TIMESTAMP: timeOperator,
    TIMESTAMP: timeOperator,
    STRING: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'LIKE',
      'NOT_LIKE',
      'START_WITH',
      'NOT_START_WITH',
      'END_WITH',
      'NOT_END_WITH',
      'IN',
      'NOT_IN',
    ],
    BOOL: ['IS_TRUE', 'IS_FALSE'],
  },
  userGroupTypeOperator: {
    INT: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    LONG: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    DOUBLE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
    HIVE_DATE: userGroupTimeOperator,
    DATE: userGroupTimeOperator,
    DATETIME: userGroupTimeOperator,
    HIVE_TIMESTAMP: userGroupTimeOperator,
    TIMESTAMP: userGroupTimeOperator,
    STRING: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'LIKE',
      'NOT_LIKE',
      'START_WITH',
      'NOT_START_WITH',
      'END_WITH',
      'NOT_END_WITH',
      'IN',
      'NOT_IN',
    ],
    BOOL: ['IS_TRUE', 'IS_FALSE'],
  },
  typeFormatter: {
    DATE: 'YYYY-MM-DD',
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    TIMESTAMP: 'YYYY-MM-DD HH:mm:ss',
    HIVE_DATE: 'YYYY-MM-DD',
    HIVE_TIMESTAMP: 'YYYY-MM-DD HH:mm:ss',
  },
  connector: [
    {
      name: '且',
      value: 'AND',
    },
    {
      name: '或',
      value: 'OR',
    },
  ],
  validator: {
    STRING: {
      option: {
        required: true,
        maxLen: 500,
      },
      message: {
        required: '请输入',
        maxLen: '最大输入500个字符',
      },
    },
    INT: {
      option: {
        required: true,
        maxLen: 11,
        regex: '^[+-]?\\d*$',
      },
      message: {
        required: '请输入',
        maxLen: '最大长度11个字符',
        regex: '请输入数字',
      },
    },
    LONG: {
      option: {
        required: true,
        maxLen: 20,
        regex: '^[+-]?\\d*$',
      },
      message: {
        required: '请输入',
        maxLen: '最大长度20个字符',
        regex: '请输入数字',
      },
    },
    DOUBLE: {
      option: {
        required: true,
        // regex: '^\\d*[.]?\\d*$',
        regex: '^[+-]?\\d+(\\.\\d+)?$',
        maxLen: 20,
      },
      message: {
        required: '请输入',
        maxLen: '最大长度20个字符',
        regex: '请输入浮点数字',
      },
    },
    DATETIME: {
      option: {
        required: true,
      },
      message: {
        required: '请输入日期时间',
      },
    },
    TIMESTAMP: {
      option: {
        required: true,
      },
      message: {
        required: '请输入日期时间',
      },
    },
    DATE: {
      option: {
        required: true,
      },
      message: {
        required: '请输入日期',
      },
    },
    HIVE_TIMESTAMP: {
      option: {
        required: true,
      },
      message: {
        required: '请输入日期时间',
      },
    },
    HIVE_DATE: {
      option: {
        required: true,
      },
      message: {
        required: '请输入日期',
      },
    },
    BOOL: {
      option: {
        required: false,
      },
      message: {
        required: '请输入',
      },
    },
  },
}
