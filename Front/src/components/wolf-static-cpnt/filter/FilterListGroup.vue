<script>
import FilterGroup from './FilterGroup.vue'
import FilterModelUtil from './FilterModelUtil'
import FilterSingle from './FilterSingle.vue'

export default {
  name: 'FilterListGroup',
  components: {
    FilterGroup,
    FilterSingle,
  },
  inject: ['filterContext'],
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    onChange: {
      type: Function,
      default: () => {},
    },
  },
  computed: {
    context() {
      return this.filterContext()
    },
    logProvider() {
      return (
        this.context.logProvider || {
          getLogger: () => ({ debug: console.log }),
        }
      )
    },
    mode() {
      return this.context.mode || 'edit'
    },
    shouldRender() {
      if (!this.value || !this.value.filters) {
        return false
      }
      const filterListCount = this.value.filters.length
      return filterListCount > 0
    },
    filterListCount() {
      return this.value.filters ? this.value.filters.length : 0
    },
  },
  mounted() {
    const log = this.logProvider.getLogger('FilterListGroup')
    // 调试日志
    if (this.value) {
      log.debug('connector', this.value.connector)
      log.debug('filters', this.value.filters)
    }
  },
  methods: {
    onChangeFilter(filterList, index) {
      return (filter) => {
        console.log('FilterListGroup onChangeFilter 接收到:', filter)
        filterList[index] = filter
        this.onChange(this.value)
      }
    },

    onAddFilter(filterList, index) {
      return () => {
        filterList.filters = [
          ...filterList.filters.slice(0, index + 1),
          FilterModelUtil.createFilter(),
          ...filterList.filters.slice(index + 1),
        ]
        this.onChange(this.value)
      }
    },

    onDeleteFilter(filterList, index) {
      const _value = this.value
      return (flag) => {
        if (flag && _value.filters.length === 0) {
          filterList.filters.splice(index, 1)
          FilterModelUtil.deleteEmptyFilterList(this.value)
          this.onChange({})
        }
        else {
          filterList.filters.splice(index, 1)
          FilterModelUtil.deleteEmptyFilterList(this.value)
          this.onChange(this.value)
        }
      }
    },

    getFilterGroupShouldRender(filterList) {
      if (!filterList || !filterList.filters)
        return false
      const filterCount
        = this.mode === 'detail' ? FilterModelUtil.getValidFilterListCount(filterList) : filterList.filters.length
      return filterCount > 0
    },

    getFilterCount(filterList) {
      return this.mode === 'detail' ? FilterModelUtil.getValidFilterListCount(filterList) : filterList.filters.length
    },

    onChangeConnector(filter) {
      return (v) => {
        filter.connector = v
        this.onChange(this.value)
      }
    },
  },
}
</script>

<template>
  <div v-if="shouldRender" class="FilterGroupListPannel">
    <FilterGroup
      :connector="value.connector"
      :on-change-connector="onChangeConnector(value)"
      :filter-count="filterListCount"
      inner=""
    >
      <template v-for="(filterList, index) in value.filters">
        <FilterGroup
          v-if="getFilterGroupShouldRender(filterList)"
          :key="index"
          :connector="filterList.connector"
          :on-change-connector="onChangeConnector(filterList)"
          :filter-count="getFilterCount(filterList)"
          inner="inner"
        >
          <FilterSingle
            v-for="(filter, i) in filterList.filters"
            :key="i"
            :value="filter"
            :on-change="onChangeFilter(filterList, i)"
            :on-add="onAddFilter(filterList, i)"
            :on-delete="onDeleteFilter(filterList, i)"
          />
        </FilterGroup>
      </template>
    </FilterGroup>
  </div>
</template>

<style scoped>
/* 样式将从filter.scss中继承 */
</style>
