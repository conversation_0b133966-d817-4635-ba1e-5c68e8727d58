<script>
import { PlusOutlined } from '@ant-design/icons-vue'
import { Button, Checkbox, InputNumber, Tag } from 'ant-design-vue'
import moment from 'moment'
import unionset from '../img/img1.png'
import intersection from '../img/img2.png'

export default {
  name: 'CampaignDetail',
  components: {
    'a-button': Button,
    'a-input-number': InputNumber,
    'a-checkbox': Checkbox,
    'a-tag': Tag,
    'plus-outlined': PlusOutlined,
  },
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      intersection,
      unionset,
    }
  },
  methods: {
    formatTime(time) {
      return time ? moment(time).format('YYYY-MM-DD HH:mm:ss') : ''
    },
  },
}
</script>

<template>
  <div class="selectConplexDetailContent">
    <div class="totalPeople">
      {{ info.customerCount || 0 }} 人
    </div>
    <div class="dateTime">
      计算时间 {{ formatTime(info.lastCalcTime) }}
    </div>
    <div class="desc">
      从参与过流程的用户中筛选出符合以下规则的用户
    </div>
    <div class="selectCampaignDetail">
      <div class="left">
        <img class="imgStyle" alt="" :src="info.connector === 'AND' ? intersection : unionset">
        <div class="connectLine" />
      </div>
      <div class="right">
        <div class="timeCondition">
          <a-button v-if="!info?.timeCondition?.show" disabled class="conditionButton" type="dashed">
            设置时间条件
          </a-button>
          <div v-if="info?.timeCondition?.show" class="conditionAll">
            <div style="margin-right: 40px">
              最近
              <span style="color: red">
                {{ info?.timeCondition?.recentlyDays }}
              </span>
              天参加过流程
            </div>
            <a-checkbox :disabled="true" :checked="info?.timeCondition?.onlyCreatedByMe">
              仅包括我创建的流程
            </a-checkbox>
          </div>
        </div>
        <div class="compaignList">
          <div name="campaignCalcLogNodes">
            <a-button
              v-if="!info.campaignCalcLogNodes || info.campaignCalcLogNodes.length === 0"
              class="plusButton"
              :disabled="true"
              type="dashed"
            >
              <plus-outlined style="margin-right: 2px" />
              添加流程
            </a-button>
            <div class="detailCompaignStyle">
              <div style="width: 70px">
                参与流程
              </div>
              <div>
                <div v-for="node in info.campaignCalcLogNodes" :key="node?.campaign?.id" style="margin-bottom: 5px">
                  <a-tag> [{{ node?.campaign?.id }}]{{ node.campaign?.name }} </a-tag>
                  <span v-if="node?.calcLogAndNodes?.length > 0">批次 </span>
                  <a-tag v-for="calcLog in node?.calcLogAndNodes" :key="calcLog?.calcLogId">
                    [{{ calcLog?.calcLogId }}]
                  </a-tag>
                  <span v-if="node?.calcLogAndNodes?.length === 1 && node?.calcLogAndNodes[0]?.flowNodeIds?.length > 0">
                    节点
                  </span>
                  <a-tag
                    v-for="nodeId in node?.calcLogAndNodes?.length === 1 ? node.calcLogAndNodes[0]?.flowNodeIds : []"
                    :key="nodeId"
                  >
                    [{{ nodeId }}]
                  </a-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="limitS">
      <a-checkbox :disabled="true" :checked="!!info.limits" />
      <span class="limitTitle">分群人数限制</span>
      <a-input-number :disabled="true" style="width: 160px" :min="0" :max="2000000" :value="info.limits" />
      <span style="margin-left: 8px">系统分群人数上限：2000000</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// 样式已经在FilterSimple.vue中定义
</style>
