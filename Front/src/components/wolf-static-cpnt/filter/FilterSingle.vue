<script>
import { Tooltip } from 'ant-design-vue'
import lodash from 'lodash'
import FilterField from './FilterField.vue'
import FilterOperator from './FilterOperator.vue'
import FilterSingleWrapper from './FilterSingleWrapper.vue'
import FilterValue from './FilterValue.vue'

export default {
  name: 'FilterSingle',
  components: {
    'a-tooltip': <PERSON>lt<PERSON>,
    FilterSingleWrapper,
    FilterField,
    FilterOperator,
    FilterValue,
  },
  inject: ['filterContext'],
  props: {
    value: {
      type: Object,
      required: true,
    },
    onChange: {
      type: Function,
      default: () => {},
    },
    onAdd: {
      type: Function,
      default: () => {},
    },
    onDelete: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      lodash,
      validator: {},
    }
  },
  computed: {
    context() {
      return this.filterContext()
    },
    // todo React 写法 const { logProvider, canAdd, mode, hideAdd, hideInit } = useContext(FilterContext); 转成vue就是下方写法
    logProvider() {
      return (
        this.context.logProvider || {
          getLogger: () => ({ debug: window.console.log }),
        }
      )
    },
    canAdd() {
      return this.context.canAdd || false
    },
    mode() {
      return this.context.mode || 'edit'
    },
    hideAdd() {
      return this.context.hideAdd || false
    },
    hideInit() {
      return this.context.hideInit || false
    },
    fieldName() {
      return this.value.fieldName
    },
  },
  watch: {
    value: {
      handler() {
        if (this.mode !== 'edit')
          return
        // 更新校验器
        this.validator = this.value.valid ? this.value.valid() : {}
      },
      deep: true,
    },
  },
  mounted() {
    const log = this.logProvider.getLogger('FilterSingle')
    log.debug('Before Render', JSON.stringify(this.value))

    // todo 初始化validator，确保新创建的组件也有校验状态
    if (this.mode === 'edit') {
      this.validator = this.value.valid ? this.value.valid() : {}
    }
  },
  methods: {
    onDeleteItem() {
      this.onDelete && this.onDelete(this.hideInit)
    },
  },
}
</script>

<template>
  <li :class="`FilterSingle ${mode}`">
    <div
      style="display: flex"
      :style="{
        display: mode !== 'edit' && !value.valid().isValid ? 'none' : 'flex',
      }"
    >
      <div :class="`FilterField ${mode} ${validator?.fieldType && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="fieldName" :use-take-place-width="true">
          <FilterField :value="value" :on-change="onChange" />
        </FilterSingleWrapper>
      </div>
      <div :class="`FilterOperator ${mode} ${validator?.operator && value.validating ? 'has-error' : ''}`">
        <FilterSingleWrapper :value="value.getOperatorShow()" :use-take-place-width="true">
          <FilterOperator :value="value" :on-change="onChange" />
        </FilterSingleWrapper>
      </div>
      <div
        :class="`FilterValue ${mode} ${validator?.value && value.validating ? 'has-error' : ''}`"
        :style="{
          display: value.isValueCanEdit() === false ? 'none' : 'block',
        }"
      >
        <FilterSingleWrapper :value="value.getValueShow()">
          <FilterValue :value="value" :on-change="onChange" />
        </FilterSingleWrapper>
      </div>
      <div v-if="mode === 'edit'" class="Ctroller">
        <a-tooltip
          v-if="value.validating && (validator?.fieldType || validator?.operator || lodash.head(lodash.values(validator.value)))"
          placement="topRight"
          :title="lodash.values(validator.value)"
        >
          <div style="margin-right: 5px; color: #fa7777;">
            <a-icon type="question-circle" />
          </div>
        </a-tooltip>
        <a-icon
          v-if="!hideAdd"
          type="plus-circle"
          class="add"
          :style="{ display: !canAdd ? 'none' : 'inline-block', marginRight: '5px' }"
          @click="onAdd"
        />
        <a-icon type="close-circle" @click="onDeleteItem" />
      </div>
    </div>
  </li>
</template>

<style scoped>
/* 样式将从filter.scss中继承 */
</style>
