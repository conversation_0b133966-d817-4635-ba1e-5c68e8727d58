// @import '~antd/dist/antd.css';
@import '../../variable.scss';
$connectLineStyle: 1px dotted #ccc;
$connectLineMarginTopAndBottom: 15px;

.wolf-static-component_filter_FilterGroupPanel {
  .FilterGroupPanel {
    // border: 1px solid #ccc;
    width: 100%;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;

    .ConnectorPanel {
      // border: 1px solid #ccc;
      width: 40px;
      min-width: 40px;
      position: relative;

      .VLine {
        position: absolute;
        top: $connectLineMarginTopAndBottom;
        bottom: $connectLineMarginTopAndBottom;
        left: 50%;
        width: 0px;
        border-left: $connectLineStyle;
        z-index: 100;
      }

      .TopLine {
        position: absolute;
        top: $connectLineMarginTopAndBottom;
        left: 50%;
        height: 0;
        width: 50%;
        border-top: $connectLineStyle;
        z-index: 100;
      }

      .BottomLine {
        position: absolute;
        bottom: $connectLineMarginTopAndBottom;
        left: 50%;
        height: 0;
        width: 50%;
        border-top: $connectLineStyle;
        z-index: 100;
      }

      .Connector {
        position: absolute;
        left: 0;
        top: 50%;
        height: 30px;
        line-height: 30px;
        transform: translateY(-15px);
        vertical-align: middle;
        z-index: 200;

        .ant-switch {
          background-color: $primary_color;
          opacity: 1;
        }

        .ant-switch-checked {
          background-color: $primary_color;
          opacity: 1;
        }

        .ant-switch-disabled {
          background-color: $primary_color;
          opacity: 1;
        }
      }
    }

    .FilterList {
      // border: 1px solid #ccc;
      flex: auto;
      margin: 0;
      padding: 0;
      margin: 2px 0;

      &.inner {
        background-color: #fafafa;
      }

      >li {
        margin: 2px 2px;
        list-style: none;
      }

      .FilterSingle.edit {
        .takePlace {
          min-width: 50px;
          position: absolute;
          visibility: hidden;
          width: auto;
          white-space: nowrap;
        }

        .has-error .ant-picker {
          border-color: red;
        }
      }

      .FilterSingle {
        line-height: 34px;

        &:focus {
          outline: none;
        }

        >div {
          display: inline-block;

          >div {
            display: inline-block;
          }

          .FilterField,
          .FilterOperator,
          .FilterValue {
            .FilterSingleWrapper {
              word-wrap: break-word;
              margin-right: 5px;

              .valueShow {
                color: $active_color;
                word-break: break-word;
              }
            }
          }

          .Ctroller {
            font-size: 12px;
            // line-height: 32px;
            display: flex;
            align-items: center;

            .add {
              margin: 0 2px;
              color: rgba(0, 0, 0, 0.65);
            }

            .add:hover {
              color: $primary_color;
            }
          }

          .Validator {
            color: $danger_color;
            font-size: 16px;
          }
        }
      }
    }
  }

  .clickWrapper {
    display: flex;
    align-items: center;
    position: relative;

    .anticon.anticon-down {
      position: absolute;
      right: 12px;
    }
  }
}

.selectConplexDetailContent {
  .totalPeople {
    font-size: 20px;
    margin-left: 10px;
  }

  .dateTime {
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
    margin-left: 10px;
  }

  .desc {
    // color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
    margin-top: 10px;
    margin-left: 10px;
  }

  .selectCampaignDetail {
    display: flex;

    .left {
      width: 55px;

      .connectLine {
        border-top: 1px dashed #0099ff;
        border-bottom: 1px dashed #0099ff;
        border-left: 1px dashed #0099ff;
        // margin: 32px 0px 32px 23px;
        height: 88px;
        width: 32px;
        position: relative;
        top: 10px;
        left: 23px;
      }

      .imgStyle {
        height: 22px;
        position: relative;
        top: 66px;
        left: 6px;
        z-index: 1;
      }
    }

    .right {
      flex: 1;

      .timeCondition {
        display: flex;
        background-color: #fafafa;
        align-items: flex-start;
        margin-bottom: 20px;
        height: 72px;
        padding: 16px 10px 0px 10px;

        .conditionAll {
          display: flex;
          align-items: center;
          margin-top: 5px;
        }
      }

      .compaignList {
        background-color: #fafafa;
        padding: 16px 10px;

        .compaignSelect {
          width: 260px;
          margin-right: 41px;
        }

        .detailCompaignStyle {
          display: flex;
        }
      }
    }
  }

  .limitS {
    margin-top: 20px;
    display: flex;
    align-items: center;

    .limitTitle {
      color: rgba(0, 0, 0, 0.85);
      font-weight: 600;
      font-size: 16px;
      margin-left: 8px;
      margin-right: 8px;
    }
  }
}

.conplexInfoItemPopover {
  z-index: 999;

  .ant-popover-inner-content {
    width: 560px;
    max-height: 480px;
    overflow: auto;

    .renderUserGroupInfo {
      .FilterGroupPanel3 {
        white-space: nowrap;
        overflow: auto;
      }

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .content {
        &>div {
          font-size: 14px;
          display: inline-block;
          min-width: 200px;
          margin: 10px 10px 10px 0px;
        }
      }

      .count {
        font-size: 24px;
        font-weight: bold;
        margin: 10px 0;
      }

      .lastCalcTime {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
}