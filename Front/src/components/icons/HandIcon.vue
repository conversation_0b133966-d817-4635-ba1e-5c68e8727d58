<script>
// 自定义手势图标 SVG 组件
const HandSvg = {
  render(h) {
    return h('svg', {
      attrs: {
        width: '1em',
        height: '1em',
        viewBox: '0 0 16 16',
        fill: 'currentColor',
      },
    }, [
      h('path', {
        attrs: {
          d: 'M7.99958 0.333496V2.60964H6.66625V0.333496H7.99958ZM3.33291 2.0567L4.94239 3.66618L3.99958 4.60899L2.3901 2.99951L3.33291 2.0567ZM12.2757 2.99951L10.6662 4.60899L9.72344 3.66618L11.3329 2.0567L12.2757 2.99951ZM7.33278 4.54016C7.05455 4.54016 6.829 4.76571 6.829 5.04395V11.7317L3.83023 11.0649L3.76316 11.1656L6.04665 14.1359C6.14214 14.2602 6.28993 14.333 6.44662 14.333H11.1703C11.3875 14.333 11.5803 14.194 11.6489 13.9879L12.8372 10.4209C12.9126 10.1944 12.819 9.94606 12.6129 9.82572L10.1964 8.41526C10.1192 8.37021 10.0315 8.34647 9.94211 8.34647H7.83656V5.04395C7.83656 4.76571 7.61101 4.54016 7.33278 4.54016ZM5.49566 5.04395C5.49566 4.02934 6.31817 3.20683 7.33278 3.20683C8.34739 3.20683 9.1699 4.02934 9.1699 5.04395V7.01314H9.94211C10.2677 7.01314 10.5874 7.09962 10.8686 7.26374L13.285 8.6742C14.036 9.11256 14.377 10.0173 14.1022 10.8423L12.9139 14.4093C12.6639 15.1599 11.9615 15.6663 11.1703 15.6663H6.44662C5.87584 15.6663 5.33745 15.4011 4.98957 14.9486L2.12402 11.2211L2.75143 10.2794C3.03828 9.84883 3.56036 9.639 4.06538 9.7513L5.49566 10.0693V5.04395Z',
          fill: '#86909C',
        },
      }),
    ])
  },
}

export default {
  name: 'HandIcon',
  inheritAttrs: false,
  data() {
    return {
      HandSvg,
    }
  },
}
</script>

<template>
  <a-icon :component="HandSvg" v-bind="$attrs" />
</template>
