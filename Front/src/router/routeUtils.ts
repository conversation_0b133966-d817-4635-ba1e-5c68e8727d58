import type { RouteConfig } from 'vue-router'

export interface CustomRouteMeta {
  title?: string
  skipLayout?: boolean
}

export interface CustomRouteConfig {
  path: string
  name?: string
  component?: any
  meta?: CustomRouteMeta
}

export interface ProcessedRoutes {
  layoutChildren: CustomRouteConfig[]
  topLevelRoutes: RouteConfig[]
}

/**
 * 处理路由配置的函数
 * @param routes 原始路由配置数组
 * @param basePath 基础路径，默认为 "/home"
 * @returns 处理后的路由配置对象
 */
export function processRoutes(routes: CustomRouteConfig[], basePath: string = '/home'): ProcessedRoutes {
  const layoutChildren: CustomRouteConfig[] = []
  const topLevelRoutes: RouteConfig[] = []

  routes.forEach((route: CustomRouteConfig) => {
    if (route.meta && route.meta.skipLayout) {
      // 跳过布局的路由，添加到顶级路由
      topLevelRoutes.push({
        ...route,
        path: `${basePath}/${route.path}`.replace(/\/+/g, '/'), // 处理多余的斜杠
      })
    }
    else {
      // 正常的子路由
      layoutChildren.push(route)
    }
  })

  return { layoutChildren, topLevelRoutes }
}

/**
 * 打印路由配置调试信息
 * @param layoutChildren 布局内的子路由
 * @param topLevelRoutes 跳过布局的顶级路由
 */
export function logRouteConfig(layoutChildren: CustomRouteConfig[], topLevelRoutes: RouteConfig[]): void {
  console.log(
    '布局内的子路由:',
    layoutChildren.map(r => ({ path: r.path, name: r.name })),
  )
  console.log(
    '跳过布局的顶级路由:',
    topLevelRoutes.map(r => ({ path: r.path, name: r.name })),
  )
}
