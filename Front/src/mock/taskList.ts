export const taskList = [
  {
    clid: 32266,
    task_name: '2024-10月资产提升目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: '',
    calc_time: '2020-06-16 00:00:00',
    start_time: '2025/5/1 0:00:00',
    end_time: '2025/5/1 0:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 34679,
    task_name: '2024-10月资产提升目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'wait',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 32280,
    task_name: '2024-10月资产提升目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'running',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 34681,
    task_name: '2024-10月资产提升目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'success',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 32282,
    task_name: '2024-10月资产提升目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'success',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 34683,
    task_name: '2024-10月资产提升目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'error',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 32284,
    task_name: '2024-10月资产提升目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'success',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 34685,
    task_name: '2024-10月资产提升目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'success',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 32286,
    task_name: '2024-10月资产提升目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'success',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 34687,
    task_name: '2024-10月资产提升目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'success',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
]

export const taskList2 = [
  {
    clid: 32266,
    task_name: '2024-10月人群扩散目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: '',
    calc_time: '2020-06-16 00:00:00',
    start_time: '2025/5/1 0:00:00',
    end_time: '2025/5/1 0:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 34679,
    task_name: '22024-10月人群扩散目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'wait',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 32280,
    task_name: '2024-10月人群扩散目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'running',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 34681,
    task_name: '2024-10月人群扩散目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'success',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 32282,
    task_name: '2024-10月人群扩散目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'success',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 34683,
    task_name: '2024-10月人群扩散目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'error',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 32284,
    task_name: '2024-10月人群扩散目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'success',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 34685,
    task_name: '2024-10月人群扩散目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'success',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 32286,
    task_name: '2024-10月人群扩散目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'success',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
  {
    clid: 34687,
    task_name: '2024-10月人群扩散目标客户预测',
    idType: '用户编号',
    calcRule: '单次计算',
    calcStatus: 'success',
    calc_time: '2020-06-16 00:00:00',
    createUserName: 'kyle.tan',
    createTime: '2020-06-20 10:28:51',
    updateUserName: 'kyle.tan',
    updateTime: '2020-06-20 10:28:51',
  },
]
