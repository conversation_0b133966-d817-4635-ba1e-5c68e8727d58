export function getMenu() {
  return {
    header: {
      code: 0,
    },
    body: {
      menus: {
        main: [
          {
            id: 1,
            name: '商机挖掘',
            code: 'analyzer_business_opportunity',
            parentId: 0,
            orderNum: 1,
            icon: '',
            route: '',
            children: [
              {
                id: 18,
                name: '智能实时挖潜',
                code: 'analyzer_segment',
                parentId: 15,
                orderNum: 1,
                route: '/home/<USER>/real_time_opp',
                domain: 'http://wolf.dev.datatist.cn',
                children: [],
              },
              {
                id: 12,
                name: '智能批量挖潜',
                code: 'analyzer_batch_opportunity',
                parentId: 1,
                orderNum: 2,
                route: '/home/<USER>/offline_opp',
                children: [],
              },
              // {
              //   id: 13,
              //   name: "人群扩散商机",
              //   code: "analyzer_crowd_opportunity",
              //   parentId: 1,
              //   route: "/home/<USER>/crowd_opp",
              //   children: []
              // },
              {
                id: 14,
                name: '模型挖掘商机',
                code: 'analyzer_model_opportunity',
                parentId: 1,
                route: '/home/<USER>/model_opp',
                children: [],
              },
              {
                id: 15,
                name: '自定义挖掘商机',
                code: 'analyzer_custom_opportunity',
                parentId: 1,
                orderNum: 5,
                route: '/home/<USER>/custom_opp',
                children: [],
              },
            ],
          },
          {
            id: 2,
            name: '营销要素模型',
            code: 'analyzer_marketing_model',
            parentId: 0,
            orderNum: 2,
            icon: '',
            route: '/marketing/model',
            children: [],
          },
          {
            id: 3,
            name: '智能指标预测',
            code: 'analyzer_prediction',
            parentId: 0,
            orderNum: 3,
            icon: '',
            route: '/home/<USER>',
            children: [],
          },
          {
            id: 4,
            name: '数据源管理',
            code: 'analyzer_data_source',
            parentId: 0,
            orderNum: 4,
            icon: '',
            route: '',
            children: [
              {
                id: 41,
                name: '事件管理',
                code: 'analyzer_event_manage',
                parentId: 4,
                orderNum: 1,
                route: '/data/event',
                children: [],
              },
              {
                id: 42,
                name: '远程数据项',
                code: 'analyzer_remote_data',
                parentId: 4,
                orderNum: 2,
                route: '/data/remote',
                children: [],
              },
              {
                id: 43,
                name: '本地数据项',
                code: 'analyzer_local_data',
                parentId: 4,
                orderNum: 3,
                route: '/data/local',
                children: [],
              },
            ],
          },
          {
            id: 5,
            name: '监控预警',
            code: 'analyzer_monitor',
            parentId: 0,
            orderNum: 5,
            icon: '',
            route: '/monitor',
            children: [],
          },
          {
            id: 6,
            name: '操作日志',
            code: 'analyzer_operation_log',
            parentId: 0,
            orderNum: 6,
            icon: '',
            route: '/operation/log',
            children: [],
          },
        ],
        right: [],
      },
    },
  }
}

export function getConsoleDesk() {
  return {
    header: {
      code: 0,
    },
    body: {
      menus: {
        main: [
          {
            id: 1,
            name: '商机规则管理',
            code: 'console_rule_manage',
            parentId: 0,
            orderNum: 1,
            icon: '',
            route: '/console/rule',
            children: [],
          },
          {
            id: 2,
            name: '商机分类管理',
            code: 'console_category_manage',
            parentId: 0,
            orderNum: 2,
            icon: '',
            route: '/console/category',
            children: [],
          },
          {
            id: 3,
            name: '事件分类管理',
            code: 'console_event_category',
            parentId: 0,
            orderNum: 3,
            icon: '',
            route: '/console/event',
            children: [],
          },
          {
            id: 4,
            name: '模型上下架管理',
            code: 'console_model_manage',
            parentId: 0,
            orderNum: 4,
            icon: '',
            route: '/console/model',
            children: [],
          },
          {
            id: 5,
            name: '监控预警',
            code: 'console_monitor',
            parentId: 0,
            orderNum: 5,
            icon: '',
            route: '/console/monitor',
            children: [],
          },
          {
            id: 6,
            name: '权限管理',
            code: 'console_permission',
            parentId: 0,
            orderNum: 6,
            icon: '',
            route: '/console/permission',
            children: [],
          },
          {
            id: 7,
            name: '应用管理',
            code: 'console_application',
            parentId: 0,
            orderNum: 7,
            icon: '',
            route: '/console/application',
            children: [],
          },
          {
            id: 8,
            name: '集成管理',
            code: 'console_integration',
            parentId: 0,
            orderNum: 8,
            icon: '',
            route: '/console/integration',
            children: [],
          },
          {
            id: 9,
            name: '审批管理',
            code: 'console_approval',
            parentId: 0,
            orderNum: 9,
            icon: '',
            route: '/console/approval',
            children: [],
          },
          {
            id: 10,
            name: '审批日志',
            code: 'console_approval_log',
            parentId: 0,
            orderNum: 10,
            icon: '',
            route: '/console/approval-log',
            children: [],
          },
          {
            id: 11,
            name: '安全设置',
            code: 'console_security',
            parentId: 0,
            orderNum: 11,
            icon: '',
            route: '/console/security',
            children: [],
          },
          {
            id: 12,
            name: '操作日志',
            code: 'console_operation_log',
            parentId: 0,
            orderNum: 12,
            icon: '',
            route: '/console/operation-log',
            children: [],
          },
        ],
        right: [],
      },
    },
  }
}
