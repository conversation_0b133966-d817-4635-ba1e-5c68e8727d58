<!--
 * <AUTHOR> 韦明良
 * @Date         : 2025年06月17 13:01:04
 * @LastEditors  : 韦明良
 * @LastEditTime : 2025年06月21 17:40:22
 * @Description  :
-->
<script>
import FilterCondition from '@/pages/home/<USER>/components/FilterCondition.vue'

export default {
  name: 'WorkspaceJsonRealTimeAdd',
  components: {
    FilterCondition,
  },
  data() {
    return {
      pageType: 'add',
      steps: 0,
      formData: {
        id: '1',
        name: '',
        timeType: '',
      },
      rules: {
        // id: [{ required: true, message: "请选择ID类型", trigger: "blur" }],
        name: [{ required: true, message: '请输入商机名称', trigger: 'change' }],
        timeType: [{ required: true, message: '请选择有效时间', trigger: 'change' }],
      },
      routes: [
        {
          // path: '/aimarketer/business/RealTime',
          breadcrumbName: '实时商机',
        },
        {
          // path: '/aimarketer/business/RealTimeAdd',
          breadcrumbName: '新增',
        },
      ],
      formItemLayout: {
        labelCol: { span: 6 },
        wrapperCol: { span: 14 },
      },
      filterConfig: {
        a: 1,
        router: '/home/<USER>/real_time_opp',
      },
    }
  },

  mounted() {
    const type = this.$route.query.type
    if (type == 'edit') {
      this.pageType = type
      this.formData = {
        id: '1',
        name: '浏览理财页面断点商机',
        timeType: '1',
        desc: '浏览理财页面断点商机',
        type: true,
      }
    }
  },

  methods: {
    handleUpdated(value) {
      console.log(value)
      this.steps = 0
    },
    handOut() {
      this.$router.go(-1)
    },
    handleSubmit(e) {
      this.steps = 1
      return
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // alert("submit!");
          this.steps = 1
        }
        else {
          console.log('error submit!!')
          return false
        }
      })
    },
  },
}
</script>

<template>
  <div>
    <!-- header -->
    <div class="header bg-white p-[10px_20px]">
      <div class="mb-20">
        <a-breadcrumb>
          <a-breadcrumb-item>
            <router-link to="/home/<USER>/real_time_opp">
              智能实时挖潜
            </router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            {{ pageType == "edit" ? "编辑" : "新增" }}
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      <h2 class="text-[18px] font-bold">
        {{ pageType == "edit" ? "编辑" : "新增" }}商机
      </h2>
    </div>

    <div class="addPage bg-white m-[20px_20px] p-[30px]">
      <div class="w-[30%] m-auto">
        <a-steps :current="steps" size="small">
          <a-step title="基本信息">
            <a-icon slot="icon" type="form" />
          </a-step>
          <a-step title="设置规则">
            <a-icon slot="icon" type="solution" />
          </a-step>
        </a-steps>
      </div>
      <!-- 第一步 -->
      <div v-if="steps === 0">
        <a-form-model
          ref="ruleForm"
          class="mt-[20px] w-[40%] m-auto mb-[100px]"
          :model="formData"
          :label-col="formItemLayout.labelCol"
          :wrapper-col="formItemLayout.wrapperCol"
          :rules="rules"
        >
          <a-form-model-item label="业务归属">
            <span class="ant-form-text"> 零售 </span>
          </a-form-model-item>
          <a-form-model-item label="ID类型" prop="id" required>
            <a-select v-model="formData.id" disabled placeholder="请选择ID类型">
              <a-select-option value="1">
                CIFID
              </a-select-option>
              <a-select-option value="2">
                动账行为
              </a-select-option>
            </a-select>
          </a-form-model-item>

          <a-form-model-item label="商机名称" prop="name" required>
            <a-input v-model="formData.name" placeholder="请输入商机名称" />
          </a-form-model-item>
          <a-form-model-item label="有效时间" prop="timeType" required>
            <div class="flex items-center justify-between">
              <a-radio-group v-model="formData.timeType" class="flex-shrink-0">
                <a-radio value="1">
                  永久有效
                </a-radio>
                <a-radio value="2">
                  <span class="mr-[10px]"> 有效时间</span>
                  <a-range-picker v-model="formData.time" class="flex-shrink-0" :disabled="formData.timeType != 2" />
                </a-radio>
              </a-radio-group>
            </div>
          </a-form-model-item>
          <a-form-model-item label="备注描述" prop="desc">
            <a-textarea v-model="formData.desc" placeholder="备注描述" :rows="4" />
          </a-form-model-item>
          <a-form-model-item label="标记为测试商机" prop="type">
            <a-switch v-model="formData.type" />
          </a-form-model-item>
          <a-form-model-item label="实时计算" prop="calcRule">
            <a-switch v-model="formData.calcRule" />
          </a-form-model-item>
        </a-form-model>
        <div
          class="footer bg-white p-[20px_30px] fixed bottom-0 left-0 w-full z-10 flex justify-between"
          style="box-shadow: 0 -4px 10px #0000001f"
        >
          <a-button @click="handOut">
            退出
          </a-button>
          <a-button type="primary" @click="handleSubmit">
            下一步
          </a-button>
        </div>
      </div>
      <!-- 第二步 -->
      <!-- 筛选 -->
      <FilterCondition v-if="steps === 1" :config="filterConfig" :hour-or-minute="true" @updated="handleUpdated" />
    </div>
  </div>
</template>
