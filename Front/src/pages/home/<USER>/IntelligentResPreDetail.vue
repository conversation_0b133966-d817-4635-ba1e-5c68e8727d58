<!--
 * <AUTHOR> 李帅
 * @Date         : 2025年06月19
 * @LastEditors  : 韦明良
 * @LastEditTime : 2025年06月19
 * @Description  :
-->

<script>
import { Chart } from '@antv/g2'
import { IntelligentResPreList } from '@/mock/IntelligentResPre'

export default {
  name: 'IntelligentResultPredictionDetail',
  data() {
    return {
      data: IntelligentResPreList,
      chartPM: null,
      chartData: [
        { month: '2025/5/1', city: '实际手机银行每日登录次数', temperature: 1.50000024999987 * 1000000 },
        { month: '2025/5/2', city: '实际手机银行每日登录次数', temperature: 2.00000049999975 * 1000000 },
        { month: '2025/5/3', city: '实际手机银行每日登录次数', temperature: 2.50000074999962 * 1000000 },
        { month: '2025/5/4', city: '实际手机银行每日登录次数', temperature: 3.0000009999995 * 1000000 },
        { month: '2025/5/5', city: '实际手机银行每日登录次数', temperature: 3.50000124999937 * 1000000 },
        { month: '2025/5/6', city: '实际手机银行每日登录次数', temperature: 4.00000149999925 * 1000000 },
        { month: '2025/5/7', city: '实际手机银行每日登录次数', temperature: 4.50000174999912 * 1000000 },
        { month: '2025/5/8', city: '实际手机银行每日登录次数', temperature: 5.000001999999 * 1000000 },
        { month: '2025/5/9', city: '实际手机银行每日登录次数', temperature: 5.50000224999887 * 1000000 },
        { month: '2025/5/10', city: '实际手机银行每日登录次数', temperature: 6.00000249999875 * 1000000 },
        { month: '2025/5/11', city: '实际手机银行每日登录次数', temperature: 6.50000274999862 * 1000000 },
        { month: '2025/5/12', city: '实际手机银行每日登录次数', temperature: 7.0000029999985 * 1000000 },
        { month: '2025/5/13', city: '实际手机银行每日登录次数', temperature: 7.50000324999837 * 1000000 },
        { month: '2025/5/14', city: '实际手机银行每日登录次数', temperature: 8.00000349999825 * 1000000 },
        { month: '2025/5/15', city: '实际手机银行每日登录次数', temperature: 8.50000374999812 * 1000000 },
        { month: '2025/5/16', city: '实际手机银行每日登录次数', temperature: 9.000003999998 * 1000000 },
        { month: '2025/5/17', city: '实际手机银行每日登录次数', temperature: 9.50000424999787 * 1000000 },
        { month: '2025/5/18', city: '实际手机银行每日登录次数', temperature: 10.0000044999977 * 1000000 },
        { month: '2025/5/19', city: '实际手机银行每日登录次数', temperature: 10.5000047499976 * 1000000 },
        { month: '2025/5/20', city: '实际手机银行每日登录次数', temperature: 11.0000049999975 * 1000000 },
        { month: '2025/5/21', city: '实际手机银行每日登录次数', temperature: 11.5000052499973 * 1000000 },
        { month: '2025/5/22', city: '实际手机银行每日登录次数', temperature: 12.0000054999972 * 1000000 },
        { month: '2025/5/23', city: '实际手机银行每日登录次数', temperature: 12.5000057499971 * 1000000 },
        { month: '2025/5/24', city: '实际手机银行每日登录次数', temperature: 13.000005999997 * 1000000 },
        { month: '2025/5/25', city: '实际手机银行每日登录次数', temperature: 13.5000062499968 * 1000000 },
        { month: '2025/5/26', city: '实际手机银行每日登录次数', temperature: 14.0000064999967 * 1000000 },
        { month: '2025/5/27', city: '实际手机银行每日登录次数', temperature: 14.5000067499966 * 1000000 },
        { month: '2025/5/28', city: '实际手机银行每日登录次数', temperature: 15.0000069999965 * 1000000 },
        { month: '2025/5/29', city: '实际手机银行每日登录次数', temperature: 15.5000072499963 * 1000000 },
        { month: '2025/5/30', city: '实际手机银行每日登录次数', temperature: 16.0000074999962 * 1000000 },
        { month: '2025/5/31', city: '实际手机银行每日登录次数', temperature: 16.5000074999962 * 1000000 },
        /* { month: "2025/5/1", city: "预测手机银行每日登录次数", temperature: 3.00000049999975 * 1000000 },
          { month: "2025/5/2", city: "预测手机银行每日登录次数", temperature: 4.0000009999995 * 1000000 },
          { month: "2025/5/3", city: "预测手机银行每日登录次数", temperature: 5.00000149999925 * 1000000 },
          { month: "2025/5/4", city: "预测手机银行每日登录次数", temperature: 6.000001999999 * 1000000 },
          { month: "2025/5/5", city: "预测手机银行每日登录次数", temperature: 7.00000249999875 * 1000000 },
          { month: "2025/5/6", city: "预测手机银行每日登录次数", temperature: 8.0000029999985 * 1000000 },
          { month: "2025/5/7", city: "预测手机银行每日登录次数", temperature: 9.00000349999825 * 1000000 },
          { month: "2025/5/8", city: "预测手机银行每日登录次数", temperature: 10.000003999998 * 1000000 },
          { month: "2025/5/9", city: "预测手机银行每日登录次数", temperature: 11.0000044999977 * 1000000 },
          { month: "2025/5/10", city: "预测手机银行每日登录次数", temperature: 12.0000049999975 * 1000000 },
          { month: "2025/5/11", city: "预测手机银行每日登录次数", temperature: 13.0000054999972 * 1000000 },
          { month: "2025/5/12", city: "预测手机银行每日登录次数", temperature: 14.000005999997 * 1000000 },
          { month: "2025/5/13", city: "预测手机银行每日登录次数", temperature: 15.0000064999967 * 1000000 },
          { month: "2025/5/14", city: "预测手机银行每日登录次数", temperature: 16.0000069999965 * 1000000 },
          { month: "2025/5/15", city: "预测手机银行每日登录次数", temperature: 17.0000074999962 * 1000000 },
          { month: "2025/5/16", city: "预测手机银行每日登录次数", temperature: 18.000007999996 * 1000000 },
          { month: "2025/5/17", city: "预测手机银行每日登录次数", temperature: 19.0000084999957 * 1000000 },
          { month: "2025/5/18", city: "预测手机银行每日登录次数", temperature: 20.0000089999955 * 1000000 },
          { month: "2025/5/19", city: "预测手机银行每日登录次数", temperature: 21.0000094999952 * 1000000 },
          { month: "2025/5/20", city: "预测手机银行每日登录次数", temperature: 22.000009999995 * 1000000 },
          { month: "2025/5/21", city: "预测手机银行每日登录次数", temperature: 23.0000104999947 * 1000000 },
          { month: "2025/5/22", city: "预测手机银行每日登录次数", temperature: 24.0000109999945 * 1000000 },
          { month: "2025/5/23", city: "预测手机银行每日登录次数", temperature: 25.0000114999942 * 1000000 },
          { month: "2025/5/24", city: "预测手机银行每日登录次数", temperature: 26.000011999994 * 1000000 },
          { month: "2025/5/25", city: "预测手机银行每日登录次数", temperature: 27.0000124999937 * 1000000 },
          { month: "2025/5/26", city: "预测手机银行每日登录次数", temperature: 28.0000129999935 * 1000000 },
          { month: "2025/5/27", city: "预测手机银行每日登录次数", temperature: 29.0000134999932 * 1000000 },
          { month: "2025/5/28", city: "预测手机银行每日登录次数", temperature: 30.000013999993 * 1000000 },
          { month: "2025/5/29", city: "预测手机银行每日登录次数", temperature: 31.0000144999927 * 1000000 },
          { month: "2025/5/30", city: "预测手机银行每日登录次数", temperature: 32.0000149999925 * 1000000 },
          { month: "2025/5/31", city: "预测手机银行每日登录次数", temperature: 32.5000149999925 * 1000000 } */
      ],
    }
  },
  mounted() {
    this.renderChart()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    // 销毁图表避免内存泄漏
    if (this.chartPM) {
      this.chartPM.destroy()
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    handleResize() {
      if (this.chartPM) {
        this.chartPM.forceFit()
      }
    },
    handlePred_metrics() {
      this.$router.push('/home/<USER>')
    },
    // 策略预测值切换
    changePred_metrics() {
      this.chartData = [
        { month: '2025/5/1', city: '实际手机银行每日登录次数', temperature: 1.50000024999987 * 1000000 },
        { month: '2025/5/2', city: '实际手机银行每日登录次数', temperature: 2.00000049999975 * 1000000 },
        { month: '2025/5/3', city: '实际手机银行每日登录次数', temperature: 2.50000074999962 * 1000000 },
        { month: '2025/5/4', city: '实际手机银行每日登录次数', temperature: 3.0000009999995 * 1000000 },
        { month: '2025/5/5', city: '实际手机银行每日登录次数', temperature: 3.50000124999937 * 1000000 },
        { month: '2025/5/6', city: '实际手机银行每日登录次数', temperature: 4.00000149999925 * 1000000 },
        { month: '2025/5/7', city: '实际手机银行每日登录次数', temperature: 4.50000174999912 * 1000000 },
        { month: '2025/5/8', city: '实际手机银行每日登录次数', temperature: 5.000001999999 * 1000000 },
        { month: '2025/5/9', city: '实际手机银行每日登录次数', temperature: 5.50000224999887 * 1000000 },
        { month: '2025/5/10', city: '实际手机银行每日登录次数', temperature: 6.00000249999875 * 1000000 },
        { month: '2025/5/11', city: '实际手机银行每日登录次数', temperature: 6.50000274999862 * 1000000 },
        { month: '2025/5/12', city: '实际手机银行每日登录次数', temperature: 7.0000029999985 * 1000000 },
        { month: '2025/5/13', city: '实际手机银行每日登录次数', temperature: 7.50000324999837 * 1000000 },
        { month: '2025/5/14', city: '实际手机银行每日登录次数', temperature: 8.00000349999825 * 1000000 },
        { month: '2025/5/15', city: '实际手机银行每日登录次数', temperature: 8.50000374999812 * 1000000 },
        { month: '2025/5/16', city: '实际手机银行每日登录次数', temperature: 9.000003999998 * 1000000 },
        { month: '2025/5/17', city: '实际手机银行每日登录次数', temperature: 9.50000424999787 * 1000000 },
        { month: '2025/5/18', city: '实际手机银行每日登录次数', temperature: 10.0000044999977 * 1000000 },
        { month: '2025/5/19', city: '实际手机银行每日登录次数', temperature: 10.5000047499976 * 1000000 },
        { month: '2025/5/20', city: '实际手机银行每日登录次数', temperature: 11.0000049999975 * 1000000 },
        { month: '2025/5/21', city: '实际手机银行每日登录次数', temperature: 11.5000052499973 * 1000000 },
        { month: '2025/5/22', city: '实际手机银行每日登录次数', temperature: 12.0000054999972 * 1000000 },
        { month: '2025/5/23', city: '实际手机银行每日登录次数', temperature: 12.5000057499971 * 1000000 },
        { month: '2025/5/24', city: '实际手机银行每日登录次数', temperature: 13.000005999997 * 1000000 },
        { month: '2025/5/25', city: '实际手机银行每日登录次数', temperature: 13.5000062499968 * 1000000 },
        { month: '2025/5/26', city: '实际手机银行每日登录次数', temperature: 14.0000064999967 * 1000000 },
        { month: '2025/5/27', city: '实际手机银行每日登录次数', temperature: 14.5000067499966 * 1000000 },
        { month: '2025/5/28', city: '实际手机银行每日登录次数', temperature: 15.0000069999965 * 1000000 },
        { month: '2025/5/29', city: '实际手机银行每日登录次数', temperature: 15.5000072499963 * 1000000 },
        { month: '2025/5/30', city: '实际手机银行每日登录次数', temperature: 16.0000074999962 * 1000000 },
        { month: '2025/5/31', city: '实际手机银行每日登录次数', temperature: 16.5000074999962 * 1000000 },
        /* { month: "2025/5/1", city: "预测手机银行每日登录次数", temperature: 3.00000049999975 * 1000000 },
          { month: "2025/5/2", city: "预测手机银行每日登录次数", temperature: 4.0000009999995 * 1000000 },
          { month: "2025/5/3", city: "预测手机银行每日登录次数", temperature: 5.00000149999925 * 1000000 },
          { month: "2025/5/4", city: "预测手机银行每日登录次数", temperature: 6.000001999999 * 1000000 },
          { month: "2025/5/5", city: "预测手机银行每日登录次数", temperature: 7.00000249999875 * 1000000 },
          { month: "2025/5/6", city: "预测手机银行每日登录次数", temperature: 8.0000029999985 * 1000000 },
          { month: "2025/5/7", city: "预测手机银行每日登录次数", temperature: 9.00000349999825 * 1000000 },
          { month: "2025/5/8", city: "预测手机银行每日登录次数", temperature: 10.000003999998 * 1000000 },
          { month: "2025/5/9", city: "预测手机银行每日登录次数", temperature: 11.0000044999977 * 1000000 },
          { month: "2025/5/10", city: "预测手机银行每日登录次数", temperature: 12.0000049999975 * 1000000 },
          { month: "2025/5/11", city: "预测手机银行每日登录次数", temperature: 13.0000054999972 * 1000000 },
          { month: "2025/5/12", city: "预测手机银行每日登录次数", temperature: 14.000005999997 * 1000000 },
          { month: "2025/5/13", city: "预测手机银行每日登录次数", temperature: 15.0000064999967 * 1000000 },
          { month: "2025/5/14", city: "预测手机银行每日登录次数", temperature: 16.0000069999965 * 1000000 },
          { month: "2025/5/15", city: "预测手机银行每日登录次数", temperature: 17.0000074999962 * 1000000 },
          { month: "2025/5/16", city: "预测手机银行每日登录次数", temperature: 18.000007999996 * 1000000 },
          { month: "2025/5/17", city: "预测手机银行每日登录次数", temperature: 19.0000084999957 * 1000000 },
          { month: "2025/5/18", city: "预测手机银行每日登录次数", temperature: 20.0000089999955 * 1000000 },
          { month: "2025/5/19", city: "预测手机银行每日登录次数", temperature: 21.0000094999952 * 1000000 },
          { month: "2025/5/20", city: "预测手机银行每日登录次数", temperature: 22.000009999995 * 1000000 },
          { month: "2025/5/21", city: "预测手机银行每日登录次数", temperature: 23.0000104999947 * 1000000 },
          { month: "2025/5/22", city: "预测手机银行每日登录次数", temperature: 24.0000109999945 * 1000000 },
          { month: "2025/5/23", city: "预测手机银行每日登录次数", temperature: 25.0000114999942 * 1000000 },
          { month: "2025/5/24", city: "预测手机银行每日登录次数", temperature: 26.000011999994 * 1000000 },
          { month: "2025/5/25", city: "预测手机银行每日登录次数", temperature: 27.0000124999937 * 1000000 },
          { month: "2025/5/26", city: "预测手机银行每日登录次数", temperature: 28.0000129999935 * 1000000 },
          { month: "2025/5/27", city: "预测手机银行每日登录次数", temperature: 29.0000134999932 * 1000000 },
          { month: "2025/5/28", city: "预测手机银行每日登录次数", temperature: 30.000013999993 * 1000000 },
          { month: "2025/5/29", city: "预测手机银行每日登录次数", temperature: 31.0000144999927 * 1000000 },
          { month: "2025/5/30", city: "预测手机银行每日登录次数", temperature: 32.0000149999925 * 1000000 },
          { month: "2025/5/31", city: "预测手机银行每日登录次数", temperature: 32.5000149999925 * 1000000 } */
      ]
      this.chartPM.changeData(this.chartData)
    },
    renderChart() {
      // 销毁旧图表
      if (this.chartPM) {
        this.chartPM.destroy()
      }
      // 创建图表实例
      this.chartPM = new Chart({
        container: this.$refs.chartPred_metrics, // DOM容器
        autoFit: true, // 自适应容器大小
        height: 400,
      })
      this.chartPM.data(this.chartData).options({
        encode: {
          x: 'month',
          y: 'temperature',
          color: 'city',
        },
        scale: {
          x: { range: [0, 1] },
          y: { nice: true },
        },
        style: {},
        axis: {
          x: { title: '', line: true },
          y: { labelFormatter: d => `${d}`, title: '', line: true },
        },
      })
      this.chartPM.line().encode('shape', 'smooth')
      this.chartPM.point().encode('shape', 'point').tooltip(false)
      // 渲染
      this.chartPM.render()
    },
  },
}
</script>

<template>
  <div class="m-[10px] box-border">
    <!-- 标题 -->
    <div class="bg-white p-[20px] pb-[4px] mb-[20px]">
      <div>
        <span class="text-[#ccc] text-[14px] cursor-pointer" @click="handlePred_metrics"> 智能指标预测 </span>
        <span class="p-[0_5px]">/</span>
        <span class="text-[#333] text-[14px]"> 预测详情 </span>
      </div>
      <div class="mt-[14px]">
        10月资产提升智能指标预测
      </div>
      <div class="flex flex-wrap mt-[14px]">
        <div class="flex mr-[24px] mb-[16px] min-w-[150px]">
          <div>策略ID：</div>
          <div>2789</div>
        </div>
        <div class="flex mr-[24px] mb-[16px] min-w-[150px]">
          <div>策略名称：</div>
          <div>2024-10月资产提升策略</div>
        </div>
        <div class="flex mr-[24px] mb-[16px] min-w-[150px]">
          <div>人群包ID：</div>
          <div>1234</div>
        </div>
        <div class="flex mr-[24px] mb-[16px] min-w-[150px]">
          <div>预测对象：</div>
          <div>流程策略</div>
        </div>
        <div class="flex mr-[24px] mb-[16px] min-w-[150px]">
          <div>开始时间：</div>
          <div>2020-11-09 10:26:37</div>
        </div>
        <div class="flex mr-[24px] mb-[16px] min-w-[150px]">
          <div>结束时间：</div>
          <div>2020-11-09 10:26:37</div>
        </div>
        <div class="flex mr-[24px] mb-[16px] min-w-[150px]">
          <div>备注：</div>
          <div>-</div>
        </div>
      </div>
    </div>
    <!-- 列表 -->
    <div class="bg-white p-[20px]">
      <div>
        <!-- 策略预测 -->
        <div>
          <div class="mb-[14px]">
            策略预测
          </div>
          <div>
            <a-select default-value="1" style="width: 240px" @change="changePred_metrics">
              <a-select-option value="1">
                手机银行每日登录次数
              </a-select-option>
              <a-select-option value="2">
                钱大掌柜每日登录次数
              </a-select-option>
              <a-select-option value="3">
                手机银行积存金购买笔数
              </a-select-option>
              <a-select-option value="4">
                手机银行积存金购买金额
              </a-select-option>
              <a-select-option value="5">
                手机银行理财产品购买笔数
              </a-select-option>
              <a-select-option value="6">
                手机银行理财产品购买金额
              </a-select-option>
            </a-select>
          </div>
          <div class="flex mt-[14px]">
            <div class="flex mr-[60px]">
              <div>开始时间：</div>
              <div>2020-11-09</div>
            </div>
            <div class="flex">
              <div>结束时间：</div>
              <div>2020-12-09</div>
            </div>
          </div>
        </div>
        <!-- 可视化图表 -->
        <div class="border-solid border-[#ccc] border-[1px] mt-[14px]">
          <div class="mt-[14px] text-[16px] font-semibold text-center">
            手机银行每日登陆次数
          </div>
          <div ref="chartPred_metrics" style="width: 100%; height: 400px" />
        </div>
      </div>
    </div>
  </div>
</template>
