<script>
export default {
  data() {
    return {

    }
  },
  methods: {
    toModelList(id) {
      this.$router.push(`/home/<USER>/model_opp/model/model_select/${id}`)
    },
    toOpp() {
      this.$router.push('/home/<USER>/model_opp')
    },
  },
}
</script>

<template>
  <div class="scene-list p-[24px] ">
    <div class="clearfix">
      <a-breadcrumb>
        <a-breadcrumb-item><span @click="toOpp">模型挖掘商机</span></a-breadcrumb-item>
        <a-breadcrumb-item class="color-[rgba(0,0,0,0.65)]">
          模型业务场景
        </a-breadcrumb-item>
      </a-breadcrumb>
      <h2 class="mt-[12px]">
        模型业务场景
      </h2>
      <div class="scene" @click="toModelList(12)">
        <div class="img">
          <img src="../../../../assets/opp/scene1.png">
        </div>
        <div class="info">
          <p class="title ellipsis lineTwo">
            人群扩散场景
          </p>
          <p class="desc ellipsis lineFour">
            多种业务目标的人群扩散预测
          </p>
          <div class="link">
            <span>2个模型</span>
            <img src="../../../../assets/opp/arrow.png" alt="">
          </div>
        </div>
      </div>
      <div class="scene" @click="toModelList(13)">
        <div class="img">
          <img src="../../../../assets/opp/scene2.png">
        </div>
        <div class="info">
          <p class="title ellipsis lineTwo">
            资产提升场景
          </p>
          <p class="desc ellipsis lineFour">
            按照客户资产度预测未来一段时间内可进行资产提升的概率
          </p>
          <div class="link">
            <span>2个模型</span>
            <img src="../../../../assets/opp/arrow.png" alt="">
          </div>
        </div>
      </div>
      <div class="scene">
        <div class="img">
          <img src="../../../../assets/opp/scene3.png">
        </div>
        <div class="info">
          <p class="title ellipsis lineTwo">
            贷款业务场景
          </p>
          <p class="desc ellipsis lineFour">
            预测活动目标客户，在未来某段时间周期内，申请各类贷款（如消费贷、汽车贷等）、以及再次申请贷款、和申请贷款体现等行为的概率
          </p>
          <div class="link">
            <span>2个模型</span>
            <img src="../../../../assets/opp/arrow.png" alt="">
          </div>
        </div>
      </div>
      <div class="scene">
        <div class="img">
          <img src="../../../../assets/opp/scene4.png">
        </div>
        <div class="info">
          <p class="title ellipsis lineTwo">
            财富类业务场景
          </p>
          <p class="desc ellipsis lineFour">
            预测有过零售类产品如理财、基金产品的偏好度，在未来某段时间周期内，会有购买或交叉购买理财、基金产品行为的概率
          </p>
          <div class="link">
            <span>2个模型</span>
            <img src="../../../../assets/opp/arrow.png" alt="">
          </div>
        </div>
      </div>
      <div class="scene">
        <div class="img">
          <img src="../../../../assets/opp/scene5.png">
        </div>
        <div class="info">
          <p class="title ellipsis lineTwo">
            信用卡业务场景
          </p>
          <p class="desc ellipsis lineFour">
            该模型预测行用卡账单客户，在未来某段时间周期内，有信用卡的开卡激活、账单分期、使用消费提升等行为的概率
          </p>
          <div class="link">
            <span>2个模型</span>
            <img src="../../../../assets/opp/arrow.png" alt="">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.clearfix {
  list-style: none;
  padding-bottom: 24px;
  min-height: 100vh;
}

.scene {
    width: 320px;
    float: left;
    background: #fff;
    border-radius: 24px;
    margin: 12px 12px 12px;
    color: #000000;
    line-height: 0;
    outline: 1px solid #fff;
    box-sizing: content-box;
    &:hover {
      outline: 2px solid  #ff6800;
      box-sizing: content-box;
    }
    .img {
      width: 100%;
      height: 202px;
      text-align: center;
      img {
        width: 100%;
        height: 100%;
        border-radius: 24px;
      }
    }
    .info {
      padding: 20px 30px 30px 30px;
    }

    .title {
      font-size: 20px;
      line-height: 28px;
      height: 56px;
      color: rgba(0, 0, 0, 0.85);
      font-weight: bold;
    }

    .desc {
      height: 80px;
      margin: 6px 0 16px 0;
      opacity: 0.65;
      font-size: 14px;
      line-height: 20px;
      word-break: break-all;
      color: rgba(0, 0, 0, 0.65);
    }
    .link {
      display: flex;
      align-items: center;
      color: rgba(0, 0, 0, 0.85);
      cursor: pointer;
      img{
        width: 30px;
        margin-left:30px;
      }
    }

}
</style>
