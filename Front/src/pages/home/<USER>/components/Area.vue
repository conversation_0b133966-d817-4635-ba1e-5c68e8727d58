<script>
import { Chart } from '@antv/g2'

export default {
  name: 'AreaChart',
  props: {
    data: {
      type: Array,
      default: [
        { user_rank: '0%', prediction: 0.0738 },
        { user_rank: '100%', prediction: 0 },
      ],
    },
    taskInfo: {
      type: Object,
      default: {
        userCount: 100,
      },
    },
  },
  data() {
    return {
      modelType: 1,
      suiji: {
        need: true,
      },
    }
  },
  mounted() {
    this.renderChart()
  },
  methods: {
    renderChart() {
      const data = this.data

      const chart = new Chart({
        container: 'area-chart',
        autoFit: true,
        height: 400,
        width: 700,
      })
      console.log(chart)

      chart.data(data)

      chart.scale('value', {
        // max: this.trueData?.length > 0 ? this.trueData[0].value : 0.5, // 刻标最大值
        formatter: v => (this.modelType === 1 ? v : `${v}%`),
      })

      chart.scale('percent', {
        tickCount: 10,
        formatter: v =>
          `用户人数（排名占比） 总人数${
            this.suiji.need
              ? Math.floor(this.taskInfo.userCount * 0.9)
              : this.taskInfo.userCount
          }`,
      })

      // chart.tooltip({
      //   showTitle: true,
      //   formatter: (datum) => {
      //     return `用户排名占比${datum}% 预测购买次数 `;
      //   },
      //   fields: ["value"],
      // });

      chart.axis('value', {
        title: {
          text: '用户排名占比',
          visible: true,
        },
        grid: {
          visible: false,
        },
        line: {
          visible: true,
          style: {
            stroke: 'black',
            lineWidth: 2,
          },
        },
        tickLine: {
          visible: true,
          style: {
            stroke: 'black',
            lineWidth: 2,
          },
        },
        label: {
          formatter: v => (this.modelType === 1 ? v : `${v}%`),
        },
      })

      chart.axis('percent', {
        title: {
          text: `用户人数 (排名占比) 总人数${
            this.suiji.need
              ? Math.floor(this.taskInfo.userCount * 0.9)
              : this.taskInfo.userCount
          }`,
          visible: true,
        },
      })

      chart.line().position('per/cent*value').size(2).color('#0759C7')
      chart.area().position('percent*value').color('#0759C7').shape('smooth')
      chart.render()
    },
  },
}
</script>

<template>
  <div style="width: 700px; height: 400px">
    <div id="area-chart" />
  </div>
</template>

<style scoped>
/* 根据需要添加样式 */
</style>
