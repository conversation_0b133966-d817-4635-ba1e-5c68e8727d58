<!--
 * <AUTHOR> 韦明良
 * @Date         : 2025年06月17 11:21:56
 * @LastEditors  : 韦明良
 * @LastEditTime : 2025年06月17 14:41:15
 * @Description  :
-->

<script>
import moment from 'moment'
import { getData, realTimeBusinessList } from '@/mock/business'

function generateUniqueId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    sorter: true,
    width: 80,
    fixed: 'left',
  },
  {
    title: '商机名称',
    dataIndex: 'opportunityName',
    width: 280,
    scopedSlots: { customRender: 'name' },
    fixed: 'left',
  },
  {
    title: '商机分类',
    dataIndex: 'category',
    width: 180,
  },
  {
    title: '商机人数',
    dataIndex: 'personnel',
    width: 100,
  },
  {
    title: '作业数',
    dataIndex: 'operateNum',
    width: 100,
  },
  {
    title: 'ID类型',
    dataIndex: 'idType',
    width: 100,
  },
  {
    title: '商机运行状态',
    dataIndex: 'runningStatus',
    scopedSlots: { customRender: 'runningStatus' },
    width: 150,
  },
  {
    title: '商机状态',
    dataIndex: 'status',
    scopedSlots: { customRender: 'status' },
    width: 120,
  },
  {
    title: '审批状态',
    dataIndex: 'approvalStatus',
    customRender: text => text || '-',
    width: 150,
  },
  {
    title: '有效时间',
    dataIndex: 'validityPeriod',
    width: 180,
  },
  {
    title: '最新商机时间',
    dataIndex: 'latestTime',
    width: 180,
    customRender: text => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    sorter: true,
  },
  {
    title: '创建人',
    width: 150,
    dataIndex: 'creator',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    customRender: text => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    sorter: true,
  },
  {
    title: '更新人',
    width: 150,
    dataIndex: 'updater',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    width: 180,
    customRender: text => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    sorter: true,
  },
  {
    title: '最新批次状态',
    dataIndex: 'latesStatus',
    scopedSlots: { customRender: 'latesStatus' },
    width: 180,
  },
  {
    title: '操作',
    dataIndex: 'set',
    className: 'td-set',
    fixed: 'right',
    width: 230,
    scopedSlots: { customRender: 'set' },
  },
]
export default {
  name: 'WorkspaceJsonRealTime',
  data() {
    return {
      loading: false,
      pagination: {
        pageSizeOptions: ['10', '20', '30', '40', '50'],
        current: 1,
        pageSize: 10,
        total: 50,
      },
      search: '',
      data: [],
      columns: columns.map((v) => {
        return { ...v, key: generateUniqueId() }
      }),
    }
  },
  watch: {
    // 监听路由中meta中type
    $route: {
      async handler(newRoute, oldRoute) {
        // 获取当前路由的 meta.type
        const type = newRoute.meta.type
        this.loading = true
        const { list, total } = await getData(realTimeBusinessList, 'custom')
        this.loading = false
        this.data = list
        this.pagination.total = total
      },
      immediate: true,
    },
  },

  mounted() {},

  methods: {
    handleCreateBusiness(type) {
      if (type == 'look') {
        this.$router.push({
          path: '/home/<USER>/custom_opp/detail',
          query: {
            type,
          },
        })
      }
      else if (type === 'edit') {
        this.$router.push({
          path: '/home/<USER>/custom_opp/edit/1',
          query: {
            type,
          },
        })
      }
      else {
        this.$router.push('/home/<USER>/custom_opp/create')
      }
    },
  },
}
</script>

<template>
  <div class="box-border">
    <!-- 标题 -->
    <div class="flex items-center justify-between p-[10px]">
      <h2 class="py-2 text-xl font-bold">
        自定义挖掘商机
      </h2>
      <div class="flex">
        <a-input v-model="search" placeholder="搜索" />
        <a-button type="primary" class="ml-[10px]" @click="handleCreateBusiness('add')">
          新建
        </a-button>
      </div>
    </div>
    <!-- 筛选条件 -->
    <div class="flex items-center justify-between p-[10px] mb-[20px]">
      <div class="flex text-[16px] items-end cursor-pointer">
        <div class="mr-[10px] text-[18px] font-bold">
          全部
        </div>
        <div class="mr-[10px]">
          我的
        </div>
        <div class="mr-[10px]">
          最近
        </div>
        <div class="mr-[10px]">
          收藏
        </div>
      </div>
      <div class="flex">
        <div class="ml-[10px]">
          <a-dropdown>
            <div class="ant-dropdown-link" @click="(e) => e.preventDefault()">
              全部更新人<a-icon type="down" />
            </div>
            <a-menu slot="overlay">
              <a-menu-item>
                <a href="javascript:;">name1</a>
              </a-menu-item>
              <a-menu-item>
                <a href="javascript:;">name2</a>
              </a-menu-item>
              <a-menu-item>
                <a href="javascript:;">name3</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </div>
        <div class="ml-[10px]">
          <a-dropdown>
            <div class="ant-dropdown-link" @click="(e) => e.preventDefault()">
              全部审批状态<a-icon type="down" />
            </div>
            <a-menu slot="overlay">
              <a-menu-item>
                <a href="javascript:;">通过</a>
              </a-menu-item>
              <a-menu-item>
                <a href="javascript:;">拒绝</a>
              </a-menu-item>
              <a-menu-item>
                <a href="javascript:;">审核中</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </div>
        <div class="ml-[10px]">
          <a-dropdown>
            <div class="ant-dropdown-link" @click="(e) => e.preventDefault()">
              全部商机状态<a-icon type="down" />
            </div>
            <a-menu slot="overlay">
              <a-menu-item>
                <a href="javascript:;">草稿</a>
              </a-menu-item>
              <a-menu-item>
                <a href="javascript:;">已上线</a>
              </a-menu-item>
              <a-menu-item>
                <a href="javascript:;">已下线</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </div>
        <div class="ml-[10px]">
          <a-dropdown>
            <div class="ant-dropdown-link" @click="(e) => e.preventDefault()">
              全部运行状态<a-icon type="down" />
            </div>
            <a-menu slot="overlay">
              <a-menu-item>
                <a href="javascript:;">健康</a>
              </a-menu-item>
              <a-menu-item>
                <a href="javascript:;">计算异常</a>
              </a-menu-item>
              <a-menu-item>
                <a href="javascript:;">数据异常</a>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </div>
      </div>
    </div>
    <!-- 列表 -->
    <div class="bg-white p-[10px]">
      <a-table :loading="loading" :columns="columns" :data-source="data" :scroll="{ x: 800 }" :pagination="pagination">
        <div slot="name" slot-scope="text">
          <a-button type="link" @click="handleCreateBusiness('look')">
            {{ text }}
          </a-button>
        </div>
        <div slot="runningStatus" slot-scope="text">
          <span
            :class="{
              'text-[#00000072]': text == '停止',
              'text-green-400': text == '健康',
              'text-red-500': text == '计算异常',
              'text-[#FF8800]': text == '服务异常',
            }"
          >{{ text }}</span>
        </div>
        <div slot="status" slot-scope="text">
          <span
            :class="{
              'text-[#00000072]': text == '草稿',
              'text-green-400': text == '已上线',
              'text-red-500': text == '已下线',
              'text-[#FF8800]': text == '服务异常',
            }"
          >{{ text }}</span>
        </div>
        <div slot="latesStatus" slot-scope="text">
          <span
            class="rounded-[100%] w-[6px] h-[6px] inline-block mr-5"
            :class="{
              'bg-[#00000072]': text == '1',
              'bg-green-400': text == '2',
              'bg-green-800': text == '3',
              'bg-red-500': text == '4',
              'bg-[#FF8800]': text == '5',
            }"
          />
          <span>{{ ["-", "待运行", "启动中", "完成", "错误", "终止"][text] }}</span>
        </div>
        <div slot="calcStatus" slot-scope="text">
          <span>{{ text }}</span>
          <span v-if="text == 'DRAFT'">草稿</span>
          <span v-else-if="text == 'NORMAL'" class="text-[#87d068]">正常</span>
        </div>

        <div slot="set" slot-scope="text, record">
          <a-button type="link" @click="handleCreateBusiness('look')">
            查看
          </a-button>
          <a-button type="link" :disabled="record.status !== '草稿'" @click="handleCreateBusiness('edit')">
            编辑
          </a-button>
          <a-button type="link">
            更多
          </a-button>
        </div>
      </a-table>
    </div>
  </div>
</template>
