<script>
import _ from 'lodash'
import { getMenu } from '../../mock/index.ts'

export default {
  name: 'Home',
  data() {
    return {
      list: [
        {
          icon:
            'http://axhub.datatist.com/pro/54XCiPO0VwS/images/%E9%A6%96%E9%A1%B5/u133.svg',
          title: '实时商机',
          desc: '超百种银行场景的商机规则库，助力运营策略优化。',
          path: '',
        },
      ],
      // 原始菜单数据
      rawMenuData: null,
      // 主菜单配置
      mainMenuConfig: [],
      // 处理后的菜单数据
      processedMainMenu: [],
    }
  },
  computed: {
    currentRoute() {
      return this.$route.path
    },
  },
  watch: {
    $route(to, from) {
      console.log('导航栏监听到路由变化:', from.path, '->', to.path)
    },
  },
  mounted() {
    console.log('Home页面已加载')
  },
  mounted() {
    console.log('NavBar组件已挂载，当前路由:', this.$route.path)

    // 加载菜单数据
    this.loadMenuData()
  },
  methods: {
    // 递归处理菜单配置 - 从React转换为Vue版本
    recursion(menuConfig) {
      if (_.isEmpty(menuConfig)) {
        return []
      }

      menuConfig.forEach((item) => {
        const hasRoute = !!item.route
        item.label = item.name
        // 设置菜单项的key
        item.key = item.route || item.name

        if (item.icon) {
          // todo 暂时把图标去掉 这里不要删
          // item.icon = () =>
          //   this.$createElement("a-icon", {
          //     props: {
          //       type: "step-backward",
          //     },
          //   });
        }
        else {
          item.icon = null
        }

        // 处理路由跳转
        item.onClick = () => {
          if (hasRoute) {
            this.$router.push(item.route)
          }
        }

        // 递归处理子菜单
        if (!_.isEmpty(item.children)) {
          this.recursion(item.children)
        }
        else {
          item.children = null
        }

        // 清理不需要的属性
        delete item.orderNum
        delete item.parentId
      })

      return menuConfig
    },

    loadMenuData() {
      try {
        const menu = getMenu().body?.menus?.main || []
        this.rawMenuData = menu
        if (!_.isEmpty(menu)) {
          this.mainMenuConfig = menu
          this.processedMainMenu = this.recursion(this.mainMenuConfig)
          console.log('NavBar - 处理后的主菜单:', this.processedMainMenu)
        }
      }
      catch (error) {
        console.error('NavBar - 加载菜单数据失败:', error)
      }
    },

    // 处理菜单点击事件
    handleMenuClick(menuItem) {
      console.log('NavBar - 菜单点击:', menuItem)

      if (menuItem.route) {
        const isLocalhost
          = window.location.hostname === 'localhost'
            || localStorage.getItem('env') === 'DEFAULT'
            || localStorage.getItem('env') === 'NS'

        if (isLocalhost) {
          // 本地环境，使用Vue Router进行内部跳转
          this.$router.push(menuItem.route)
          console.log('NavBar - 路由跳转:', menuItem.route)
        }
        else {
          // 生产环境，打开新窗口
          window.open(menuItem.route, '_blank')
          console.log('NavBar - 新窗口打开:', menuItem.route)
        }
      }
      else {
        console.log('NavBar - 菜单项没有路由:', menuItem.label)
      }
    },
  },
}
</script>

<template>
  <div class="flex">
    <div>
      <h3>常用功能</h3>
      <div class="grid grid-cols-3 gap-[10px] p-[5px]">
        <div
          v-for="(item, index) in list"
          :key="index"
          class="flex justify-around items-center p-[10px] bg-white box-border border border-[#ccc] border-solid rounded-[10px] w-[400px] h-[80px]"
        >
          <div class="flex-shrink-0 w-[50px] flex justify-center items-center">
            <img :src="item.icon" alt="" class="w-[20px] h-[20px]">
          </div>
          <div class="flex-1 p-[10px]">
            <p class="text-[16px] m-[0]">
              {{ item.title }}
            </p>
            <p class="text-[12px] text-[#ccc] m-[0]">
              {{ item.desc }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-content {
  padding: 20px;
  border: 1px #ccc;
}

.dashboard {
  margin-top: 20px;
}
</style>
