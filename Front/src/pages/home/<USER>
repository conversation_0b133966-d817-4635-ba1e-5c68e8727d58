<script>
import _ from 'lodash'
import { getConsoleDesk, getMenu } from '../../mock/index.ts'

export default {
  name: 'HomeLayout',
  data() {
    return {
      // 原始菜单数据
      rawMenuData: null,
      // 主菜单配置
      mainMenuConfig: [],
      // 选中的菜单项
      selectedKeys: [],
      // 处理后的菜单数据
      processedMainMenu: [],
      processedConsoleDesk: [],
    }
  },
  computed: {
    currentRoute() {
      return this.$route.path
    },
    isFullScreen() {
      return this.$isFullScreen || this.$route.query.fullScreen === 'true'
    },
    contentStyle() {
      // 全屏模式下调整内容区域样式
      return this.isFullScreen ? { padding: '48px' } : { padding: '24px' }
    },
  },
  watch: {
    $route(to, from) {
      this.selectedKeys = [to.path]
      console.log('导航栏监听到路由变化:', from.path, '->', to.path)
    },
  },
  mounted() {
    this.selectedKeys = [this.$route.path]
    // 加载菜单数据
    this.loadMenuData()
  },
  methods: {
    // 递归处理菜单配置 - 从React转换为Vue版本
    recursion(menuConfig) {
      if (_.isEmpty(menuConfig)) {
        return []
      }

      menuConfig.forEach((item) => {
        const hasRoute = !!item.route
        item.label = item.name
        // 设置菜单项的key
        item.key = item.route || item.name

        if (item.icon) {
          // todo 暂时把图标去掉 这里不要删
          // item.icon = () =>
          //   this.$createElement("a-icon", {
          //     props: {
          //       type: "step-backward",
          //     },
          //   });
        }
        else {
          item.icon = null
        }

        // 处理路由跳转
        item.onClick = () => {
          if (hasRoute) {
            this.$router.push(item.route)
          }
        }

        // 递归处理子菜单
        if (!_.isEmpty(item.children)) {
          this.recursion(item.children)
        }
        else {
          item.children = null
        }

        // 清理不需要的属性
        delete item.orderNum
        delete item.parentId
      })

      return menuConfig
    },

    loadMenuData() {
      try {
        const menu = getMenu().body?.menus?.main || []
        const consoleDesk = getConsoleDesk().body?.menus?.main || []
        this.rawMenuData = menu
        if (!_.isEmpty(menu)) {
          this.mainMenuConfig = menu
          this.processedMainMenu = this.recursion(this.mainMenuConfig)
        }
        if (!_.isEmpty(consoleDesk)) {
          this.consoleDesk = consoleDesk
          this.processedConsoleDesk = this.recursion(this.consoleDesk)
        }
      }
      catch (error) {
        console.error('NavBar - 加载菜单数据失败:', error)
      }
    },

    // 处理菜单点击事件
    handleMenuClick(menuItem) {
      console.log('NavBar - 菜单点击:', menuItem)

      if (menuItem.route) {
        this.$router.push(menuItem.route)
      }
      else {
        console.log('NavBar - 菜单项没有路由:', menuItem.label)
      }
    },
  },
}
</script>

<template>
  <a-layout>
    <!-- 左侧侧边栏 - 根据全屏状态控制显示 -->
    <a-layout-sider
      v-if="!isFullScreen"
      class="no-scrollbar overflow-auto"
      :width="256"
      theme="light"
      :collapsed="false"
    >
      <div class="logo">
        <span>工作台</span>
      </div>

      <!-- 左侧菜单 -->
      <a-menu mode="inline" class="nav-menu" :selected-keys="selectedKeys">
        <a-menu-item key="/home" @click="$router.push('/home')">
          <!-- <a-icon type="home" /> -->
          首页
        </a-menu-item>

        <!-- 渲染processedMainMenu -->
        <template v-for="item in processedMainMenu">
          <!-- 有子菜单的一级菜单项 -->
          <a-sub-menu v-if="item.children && item.children.length > 0" :key="item.key">
            <template slot="title">
              <span :title="item.label">{{ item.label }}</span>
            </template>
            <!-- 二级菜单项 -->
            <a-menu-item v-for="child in item.children" :key="child.key" @click="handleMenuClick(child)">
              <span :title="child.label">{{ child.label }}</span>
            </a-menu-item>
          </a-sub-menu>

          <!-- 没有子菜单的一级菜单项 -->
          <a-menu-item v-else :key="item.key" @click="handleMenuClick(item)">
            <span :title="item.label">{{ item.label }}</span>
          </a-menu-item>
        </template>
      </a-menu>

      <div class="logo">
        <span>控制台</span>
      </div>

      <!-- 左侧菜单 -->
      <a-menu mode="inline" class="nav-menu" :selected-keys="selectedKeys">
        <!-- 渲染processedMainMenu -->
        <template v-for="item in processedConsoleDesk">
          <!-- 有子菜单的一级菜单项 -->
          <a-sub-menu v-if="item.children && item.children.length > 0" :key="item.key">
            <template slot="title">
              <span :title="item.label">{{ item.label }}</span>
            </template>
            <!-- 二级菜单项 -->
            <a-menu-item v-for="child in item.children" :key="child.key" @click="handleMenuClick(child)">
              <span :title="child.label">{{ child.label }}</span>
            </a-menu-item>
          </a-sub-menu>

          <!-- 没有子菜单的一级菜单项 -->
          <a-menu-item v-else :key="item.key" @click="handleMenuClick(item)">
            <span :title="item.label">{{ item.label }}</span>
          </a-menu-item>
        </template>
      </a-menu>
    </a-layout-sider>

    <!-- 内容区域 -->
    <a-layout-content :style="contentStyle">
      <router-view />
    </a-layout-content>
  </a-layout>
</template>

<style scoped>
/* Logo区域样式 */
.logo {
  display: flex;
  font-size: 14px;
  font-weight: bold;
  padding: 16px;
  color: #c5c5c5;
}

/* 左侧菜单样式 */
.nav-menu {
  padding: 0 1px;
  border-right: none;
  overflow-x: hidden;
  overflow-y: auto;
}
.no-scrollbar {
  overflow: scroll;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ 和 Edge */
}

.no-scrollbar::-webkit-scrollbar {
  display: none; /* WebKit 内核浏览器（Chrome、Safari） */
}
</style>
