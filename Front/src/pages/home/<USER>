<script>
import myPackage from '../../../package.json'

export default {
  name: 'About',
  data() {
    return {
      deps: [],
      features: [
        {
          icon: 'component',
          title: '组件化开发',
          description: '采用Vue组件化开发模式，提高代码复用性',
        },
        {
          icon: 'mobile',
          title: '响应式设计',
          description: '支持多种屏幕尺寸，适配移动端和桌面端',
        },
        {
          icon: 'rocket',
          title: '高性能',
          description: '优化的构建配置，快速的开发和生产环境',
        },
        {
          icon: 'tool',
          title: '易于维护',
          description: '清晰的代码结构和完善的文档',
        },
      ],
      color: ['blue', 'green', 'orange', 'purple', 'cyan', 'red'],
    }
  },
  mounted() {
    this.deps = [...Object.keys(myPackage.devDependencies), ...Object.keys(myPackage.dependencies)]
  },
}
</script>

<template>
  <div class="about">
    <a-page-header title="关于项目" sub-title="项目详细信息" @back="$router.go(-1)">
      <template slot="extra">
        <a-button @click="$router.push('/')">
          <a-icon type="home" />
          返回首页
        </a-button>
      </template>
    </a-page-header>

    <div class="content">
      <a-row :gutter="24">
        <a-col :span="12">
          <a-card title="info" :bordered="false">
            <p><strong>项目名称：</strong>xingye-static</p>
            <p><strong>版本：</strong>1.0.0</p>
            <p><strong>框架：</strong>Vue 2.7.16</p>
            <p><strong>UI库：</strong>Ant Design Vue 1.x</p>
            <p><strong>构建工具：</strong>Rsbuild</p>
            <p><strong>包管理器：</strong>pnpm</p>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="package" :bordered="false">
            <a-tag v-for="(item, index) in deps" :key="item" :color="color[index % color.length]">
              {{ item }}
            </a-tag>
          </a-card>
        </a-col>
      </a-row>

      <a-card title="项目特性" style="margin-top: 24px">
        <a-list :data-source="features" render-item="item">
          <a-list-item slot="renderItem" slot-scope="item">
            <a-list-item-meta>
              <a-icon slot="avatar" :type="item.icon" style="color: #1890ff" />
              <span slot="title">{{ item.title }}</span>
              <span slot="description">{{ item.description }}</span>
            </a-list-item-meta>
          </a-list-item>
        </a-list>
      </a-card>

      <a-card title="开发团队" style="margin-top: 24px">
        <a-descriptions :column="2">
          <a-descriptions-item label="开发者">
            Vue开发团队
          </a-descriptions-item>
          <a-descriptions-item label="项目类型">
            组件库
          </a-descriptions-item>
          <a-descriptions-item label="开发时间">
            2024年
          </a-descriptions-item>
          <a-descriptions-item label="维护状态">
            活跃开发中
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>
  </div>
</template>

<style scoped>
.about {
  padding: 20px;
}

.content {
  padding: 20px 0;
}

.ant-tag {
  margin: 4px;
}
</style>
