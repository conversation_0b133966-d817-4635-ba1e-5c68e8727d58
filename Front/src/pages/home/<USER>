<script>
import { SelectTime } from '@/components/wolf-static-cpnt'

export default {
  name: 'Components',
  components: {
    SelectTime,
  },
  data() {
    return {
      timeValue: [],
    }
  },
  mounted() {
    window.console.log('Components页面已加载')
  },
  methods: {
    handleTimeChange(value, flag) {
      window.console.log('Time changed:', value, flag)
      this.timeValue = value
    },
    handleGrabbingUpdate(data) {
      window.console.log('Grabbing updated:', data)
      this.$message.success(`位置更新: x=${data.x}, y=${data.y}, scale=${data.scale}`)
    },
  },
}
</script>

<template>
  <div class="components">
    <a-page-header title="组件演示" sub-title="查看各种组件的使用示例" @back="$router.go(-1)">
      <template slot="extra">
        <a-button @click="$router.push('/')">
          <a-icon type="home" />
          返回首页
        </a-button>
      </template>
    </a-page-header>

    <div class="content">
      <a-card title="SelectTime组件" style="margin-bottom: 24px">
        <p>时间选择组件演示</p>
        <SelectTime :data="timeValue" :show-time="true" :is-analysis="false" @change="handleTimeChange" />
        <div style="margin-top: 16px">
          <strong>当前选择的时间：</strong>
          <pre>{{ JSON.stringify(timeValue, null, 2) }}</pre>
        </div>
      </a-card>
    </div>
  </div>
</template>

<style scoped>
.components {
  padding: 20px;
}

.content {
  padding: 20px 0;
}

pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}
</style>
