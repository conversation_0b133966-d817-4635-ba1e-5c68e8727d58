<script>
import { CPNTLabelFilter } from '@/components/wolf-static-cpnt/index'
// import FilterModelUtil from '@/components/wolf-static-cpnt/label/FilterModelUtil'
import { labelMockDataProvider } from './testLabelConfig'

// 模拟demo数据
const demoValue = {
  connector: 'AND',
  filters: [
    {
      connector: 'AND',
      filters: [
        {
          id: '578',
          label: '自动标签_STRING_2021_09_14',
          displayName: '自动标签_STRING_2021_09_14',
          operator: 'IN',
          value: [
            '自动标签_STRING_2021_09_14 mock的数据',
            '[2]上海',
            '[1]北京',
            '[3]南京',
          ],
          fieldType: 'STRING',
          dateType: 'RELATIVE',
          showValue: [
            '自动标签_STRING_2021_09_14 mock的数据',
            '[2]上海',
            '[1]北京',
            '[3]南京',
          ],
          checkUserTag: false,
          timeType: 'DAY',
          exCalendar: {},
          times: 99,
        },
      ],
    },
  ],
}

export default {
  name: 'TestLabel',
  components: {
    CPNTLabelFilter,
  },
  data() {
    return {
      jsonData: '{}',
      filterValue: {},
      mode: 'edit',
      mockDataProvider: labelMockDataProvider,
    }
  },
  watch: {
    filterValue: {
      handler(newVal) {
        window.console.log('filterValue changed:', newVal)
        this.jsonData = JSON.stringify(newVal, null, 2)
      },
      deep: true,
    },
  },
  mounted() {
    // 初始化一个空的过滤器
    // this.filterValue = FilterModelUtil.initCreateFilterGroup(true)
    this.setDemoValue()
  },
  methods: {
    handleFilterChange(validJson, fullValue) {
      window.console.log('Filter changed:', validJson, fullValue)
      this.filterValue = validJson || {}
    },

    toggleMode() {
      this.mode = this.mode === 'edit' ? 'detail' : 'edit'
    },

    clearFilter() {
      this.filterValue = {}
    },

    validateFilter() {
      if (this.$refs.labelFilterRef) {
        const isValid = this.$refs.labelFilterRef.isValid()
        window.console.log('isValid:', isValid)
        this.$message[isValid ? 'success' : 'error'](`验证结果: ${isValid ? '通过' : '失败'}`)
      }
    },

    submitJsonData() {
      try {
        this.filterValue = JSON.parse(this.jsonData)
      }
      catch (error) {
        this.$message.error(`JSON格式错误: ${error.message}`)
      }
    },

    setDemoValue() {
      this.filterValue = demoValue
    },
  },
}
</script>

<template>
  <div class="test-label-page">
    <h1>Label Filter 测试页面</h1>
    <div class="test-section">
      <h3>控制面板:</h3>
      <a-button @click="toggleMode">
        切换模式 (当前: {{ mode }})
      </a-button>
      <a-button style="margin-left: 8px" @click="clearFilter">
        清空过滤器
      </a-button>
      <a-button style="margin-left: 8px" @click="validateFilter">
        验证过滤器
      </a-button>
    </div>

    <div class="flex">
      <div class="w-[85%]">
        <div class="test-section">
          <h2>Vue2 版本的 Label Filter</h2>
          <CPNTLabelFilter
            ref="labelFilterRef"
            :value="filterValue"
            :data-provider="mockDataProvider"
            :on-change="handleFilterChange"
            :mode="mode"
            :show-init-line="true"
            :checked="false"
            :campaign-info="{}"
            :is-user-group="false"
            :is-campaign-v2="false"
          />
        </div>

        <div class="test-section">
          <h3>当前过滤器值:</h3>
          <pre>{{ JSON.stringify(filterValue, null, 2) }}</pre>
        </div>
      </div>

      <div class="w-[15%]">
        <!-- 右侧JSON编辑区域 -->
        <div class="json-editor">
          <a-button style="margin-bottom: 10px" @click="submitJsonData">
            应用JSON数据
          </a-button>
          <a-button style="margin-bottom: 10px" @click="setDemoValue">
            设置demo数据
          </a-button>
          <a-textarea v-model="jsonData" :rows="30" placeholder="在此编辑JSON数据..." />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.test-label-page {
  padding: 20px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
}

.test-section h2,
.test-section h3 {
  margin-top: 0;
  color: #333;
}

pre {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
}

.json-editor {
  padding: 10px;
}

.flex {
  display: flex;
  gap: 20px;
}

.w-\[85\%\] {
  width: 85%;
}

.w-\[15\%\] {
  width: 15%;
}
</style>
