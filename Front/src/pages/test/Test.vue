<script>
export default {
  name: 'Test',
}
</script>

<template>
  <div class="p-20">
    <div class="flex gap-24 mb-24">
      <a-button @click="$router.push('/test/event')">
        event
      </a-button>
      <a-button @click="$router.push('/test/filter')">
        filter
      </a-button>
      <a-button @click="$router.push('/test/actioncollective')">
        actioncollective
      </a-button>
      <a-button @click="$router.push('/test/segment')">
        segment
      </a-button>
      <a-button @click="$router.push('/test/label')">
        label
      </a-button>
      <a-button @click="$router.push('/test/selectTime')">
        selectTime
      </a-button>
    </div>

    <router-view />
  </div>
</template>

<style scoped></style>
