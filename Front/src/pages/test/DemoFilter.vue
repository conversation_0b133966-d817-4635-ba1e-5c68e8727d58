<script>
import { CPNTActionCollective } from '@/components/wolf-static-cpnt'
import {
  demoValue,
  demoValueMinuteOrHour,
  mockEventDataProvider,
} from '@/pages/test/TestEventConfig'

export default {
  name: 'DemoFilter',
  components: {
    CPNTActionCollective,
  },
  props: {
    mode: {
      type: String,
      default: 'edit',
    },
    hourOrMinute: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      filterValue: this.hourOrMinute ? demoValueMinuteOrHour : demoValue,
      mockEventDataProvider,
    }
  },
  methods: {
    handleFilterChange(validJson, fullValue) {
      window.console.log('Filter changed:', validJson, fullValue)
      this.filterValue = validJson || {}
    },
  },
}
</script>

<template>
  <div>
    <!-- 6.22用isActionCollection这个参数控制时间选择器 -->
    <CPNTActionCollective
      ref="actionCollectiveRef"
      :value="filterValue"
      :data-provider="mockDataProvider"
      :on-change="handleFilterChange"
      :mode="mode"
      :show-init-line="true"
      :is-action-collection="hourOrMinute"
      :is-user-group="false"
    />
  </div>
</template>

<style></style>
