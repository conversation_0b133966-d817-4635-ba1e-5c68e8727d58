const tableId = 1
const propertyItemList = [
  {
    name: '1',
    value: '1',
  },
  {
    name: '1111',
    value: '1111',
  },
  {
    name: '中国',
    value: '中国',
  },
]

const propertyList = [
  {
    field: 'bool',
    fieldType: 'BOOL',
    tableId: 31,
    schemaId: 4621,
    level1: '通用事件属性',
    level2: '',
    fieldName: '布尔',
    isEnum: false,
  },
  {
    field: 'long',
    fieldType: 'LONG',
    tableId: 31,
    schemaId: 4552,
    level1: '通用事件属性',
    level2: '',
    fieldName: 'long',
    isEnum: false,
  },
  {
    field: 'day',
    fieldType: 'TIMESTAMP',
    tableId: 31,
    schemaId: 3814,
    level1: '通用事件属性',
    level2: '',
    fieldName: 'day',
    isEnum: false,
  },
  {
    field: 'eventName',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 8,
    level1: '通用事件属性',
    level2: '',
    fieldName: '事件名称',
    isEnum: false,
  },
  {
    field: 'plugin',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 18,
    level1: '通用事件属性',
    level2: '',
    fieldName: '插件',
    isEnum: false,
  },
  {
    field: 'region',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 21,
    level1: '通用事件属性',
    level2: '',
    fieldName: '地区',
    isEnum: false,
  },
  {
    field: 'lgt',
    fieldType: 'DOUBLE',
    tableId: 31,
    schemaId: 16,
    level1: '通用事件属性',
    level2: '',
    fieldName: '经度',
    isEnum: false,
  },
  {
    field: 'lat',
    fieldType: 'DOUBLE',
    tableId: 31,
    schemaId: 15,
    level1: '通用事件属性',
    level2: '',
    fieldName: '纬度',
    isEnum: false,
  },
  {
    field: 'uaOs',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 31,
    level1: '通用事件属性',
    level2: '',
    fieldName: 'ua操作系统',
    isEnum: false,
  },
  {
    field: 'userId',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 36,
    level1: '通用事件属性',
    level2: '',
    fieldName: '用户ID',
    isEnum: false,
  },
  {
    field: 'userAgent',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 35,
    level1: '通用事件属性',
    level2: '',
    fieldName: '用户代理',
    isEnum: false,
  },
  {
    field: 'uaName',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 30,
    level1: '通用事件属性',
    level2: '',
    fieldName: 'ua名称',
    isEnum: false,
  },
  {
    field: 'title',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 26,
    level1: '通用事件属性',
    level2: '',
    fieldName: '标题',
    isEnum: false,
  },
  {
    field: 'url',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 34,
    level1: '通用事件属性',
    level2: '',
    fieldName: 'url',
    isEnum: false,
  },
  {
    field: 'language',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 14,
    level1: '通用事件属性',
    level2: '',
    fieldName: '语言',
    isEnum: false,
  },
  {
    field: 'eventTime',
    fieldType: 'TIMESTAMP',
    tableId: 31,
    schemaId: 10,
    level1: '通用事件属性',
    level2: '',
    fieldName: '事件时间',
    isEnum: false,
  },
  {
    field: 'userTags',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 37,
    level1: '通用事件属性',
    level2: '',
    fieldName: '用户标签',
    isEnum: false,
  },
  {
    field: 'sessionId',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 24,
    level1: '通用事件属性',
    level2: '',
    fieldName: '会话ID',
    isEnum: false,
  },
  {
    field: 'netType',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 17,
    level1: '通用事件属性',
    level2: '',
    fieldName: '网络类型',
    isEnum: false,
  },
  {
    field: 'key',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 13,
    level1: '通用事件属性',
    level2: '',
    fieldName: '值',
    isEnum: false,
  },
  {
    field: 'deviceId',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 6,
    level1: '通用事件属性',
    level2: '',
    fieldName: '压力_设备ID',
    isEnum: false,
  },
  {
    field: 'country',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 5,
    level1: '通用事件属性',
    level2: '',
    fieldName: '国家',
    isEnum: true,
  },
  {
    field: 'date',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 38,
    level1: '通用事件属性',
    level2: '',
    fieldName: '日期',
    isEnum: false,
  },
  {
    field: 'city',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 3,
    level1: '通用事件属性',
    level2: '',
    fieldName: '城市',
    isEnum: false,
  },
  {
    field: 'projectId',
    fieldType: 'STRING',
    tableId: 31,
    schemaId: 39,
    level1: '通用事件属性',
    level2: '',
    fieldName: '通用_项目ID',
    isEnum: false,
  },
  {
    field: 'i_var0',
    fieldType: 'INT',
    tableId: 31,
    level1: '事件专有属性',
    level2: '',
    fieldName: '年龄',
    isEnum: false,
  },
  {
    field: 's_var0',
    fieldType: 'STRING',
    tableId: 31,
    level1: '事件专有属性',
    level2: '',
    fieldName: '哈哈哈',
    isEnum: false,
  },
]
const mockDataProvider = {
  getPropertyList: (name) => {
    return propertyList
  },
  getPropertyEnumList: async (tableId, schemaId) => {
    return propertyItemList
  },
}

const demoValue = {
  connector: 'AND',
  filters: [
    {
      connector: 'AND',
      filters: [
        {
          tableId: 31,
          schemaId: 4621,
          field: 'bool',
          fieldName: '布尔',
          fieldType: 'BOOL',
          level1: '通用事件属性',
          level2: '',
          operator: 'IS_TRUE',
          value: null,
          showValue: null,
          isEnum: false,
        },
        {
          tableId: 31,
          schemaId: 5,
          field: 'country',
          fieldName: '国家',
          fieldType: 'STRING',
          level1: '通用事件属性',
          level2: '',
          operator: 'EQ',
          value: '中国',
          showValue: '中国',
          isEnum: true,
        },
      ],
    },
    {
      connector: 'AND',
      filters: [
        {
          tableId: 31,
          schemaId: 5,
          field: 'country',
          fieldName: '国家',
          fieldType: 'STRING',
          level1: '通用事件属性',
          level2: '',
          operator: 'EQ',
          value: '1111',
          showValue: '1111',
          isEnum: true,
        },
      ],
    },
  ],
}

export {
  demoValue,
  mockDataProvider,
  propertyItemList,
  propertyList,
  tableId,
}
