<script>
import VueJsonPretty from 'vue-json-pretty'
import { CPNTFilter } from '@/components/wolf-static-cpnt/index'
import { demoValue, mockDataProvider } from './TestFilterConfig'
import 'vue-json-pretty/lib/styles.css'

export default {
  name: 'TestFilter',
  components: {
    CPNTFilter,
    VueJsonPretty,
  },
  data() {
    return {
      value: {},
      mode: 'edit',
      jsonData: '{}',
      dataProvider: mockDataProvider,
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.jsonData = JSON.stringify(newVal)
      },
      deep: true,
    },
  },
  methods: {
    onChange(value) {
      window.console.log('🚀 ~ onChange ~ value:', value)
      this.value = value
    },
    toggleMode() {
      const newMode = this.mode === 'edit' ? 'detail' : 'edit'
      window.console.log('TestFilter 切换模式:', this.mode, '->', newMode)
      this.mode = newMode
    },
    submitJsonData() {
      this.value = JSON.parse(this.jsonData)
    },
    setDemoValue() {
      this.value = demoValue
    },
    copyJson() {
      this.$copyText(JSON.stringify(this.value, null, 2))
    },
    validateFilter() {
      if (this.$refs.filter) {
        const isValid = this.$refs.filter.isValid(true)
        if (isValid) {
          this.$message.success('过滤器校验通过')
        }
        else {
          window.console.log(isValid, '🚀 ~ isValid:')
          this.$message.error('过滤器校验失败，请检查配置')
        }
        return isValid
      }
      this.$message.warning('过滤器组件未找到')
      return false
    },
  },
}
</script>

<template>
  <div class="flex">
    <div class="w-[85%] ">
      <div>
        <CPNTFilter
          ref="filter"
          :data-provider="dataProvider"
          :value="value"
          :on-change="onChange"
          :mode="mode"
          add-button-text="添加过滤条件"
        />
        <a-button @click="toggleMode">
          只读切换
        </a-button>
      </div>

      <div class="test-section">
        <h3>当前过滤器值:</h3>
        <div class="mb-2">
          <a-button @click="copyJson">
            复制json
          </a-button>
          <a-button style="margin-left: 8px" @click="validateFilter">
            校验过滤器
          </a-button>
        </div>
        <!-- <pre>{{ JSON.stringify(filterValue, null, 2) }}</pre> -->
        <VueJsonPretty :data="value" />
      </div>
    </div>
    <div class="w-[15%]">
      <!-- 右侧JSON编辑区域 -->
      <div class="json-editor">
        <a-button style="margin-bottom: 10px" @click="submitJsonData">
          应用JSON数据
        </a-button>
        <a-button style="margin-bottom: 10px" @click="setDemoValue">
          设置demo数据
        </a-button>
        <a-textarea v-model="jsonData" :rows="30" placeholder="在此编辑JSON数据..." />
      </div>
    </div>
  </div>
</template>
