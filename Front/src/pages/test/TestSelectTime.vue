<script>
import VueJsonPretty from 'vue-json-pretty'
import { SelectTime } from '@/components/wolf-static-cpnt'
import 'vue-json-pretty/lib/styles.css'

export default {
  name: 'TestSelectTime',
  components: {
    SelectTime,
    VueJsonPretty,
  },
  data() {
    return {
      dateRange: [],
    }
  },
  methods: {
    handleChange(v) {
      this.dateRange = v
    },
  },
}
</script>

<template>
  <div>
    <h1>TestSelectTime</h1>
    <SelectTime show-time :data="dateRange" :on-change="handleChange" />
    <VueJsonPretty :data="dateRange" />
  </div>
</template>

<style scoped lang="scss"></style>
