<script>
import NavBar from './NavBar.vue'

export default {
  name: 'Layout',
  components: {
    NavBar,
  },
  data() {
    return {}
  },
  computed: {
    isFullScreen() {
      return this.$isFullScreen || this.$route.query.fullScreen === 'true'
    },
  },
  mounted() {},
  methods: {},
}
</script>

<template>
  <div id="layout">
    <a-layout>
      <!-- 顶部导航栏 - 根据全屏状态控制显示 -->
      <NavBar v-if="!isFullScreen" />

      <!-- 右侧内容区域 -->
      <a-layout>
        <!-- 主要内容区域 -->
        <a-layout-content class="content" :class="{ 'full-screen': isFullScreen }">
          <!-- 路由视图 -->
          <router-view />
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<style scoped>
#layout {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

/* 主要内容区域 */
.content {
  /* min-height: calc(100vh - 70px); */
  background: #f0f2f5;
  padding: 24px;
}

/* 全局布局样式 */
.ant-layout {
  height: 100%;
  /* height: calc(100vh - 64px); */
}

.ant-layout-content {
  padding: 0;
}

/* 确保内容区域不被左侧菜单遮挡 */
.ant-layout-content {
  overflow-x: auto;
  height: calc(100vh - 64px);
}

/* 全屏模式样式 */
.content.full-screen {
  height: 100vh;
  padding: 0;
}
</style>
