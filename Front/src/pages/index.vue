<script>
const datas = [
  {
    id: 0,
    list: [
      {
        name: '智能实时挖潜',
        subName: '感知用户实时行为，通过规则+模型实现实时需求挖掘，捕捉实时商机。',
      },
      {
        name: '智能批量挖潜',
        subName: '自定义用户画像和历史行为组合策略，融合模型实现潜客挖掘。',
      },
      {
        name: '客群智能分析',
        subName: '基于全行用户画像，根据种子用户挖掘相似用户，发现潜力用户。',
      },
      {
        name: '智能渠道优选',
        subName: '基于用户偏好，找到最佳触达渠道，减少客户打扰同时以最小成本实现最大化触客效率。',
      },
      {
        name: '智能文案优选',
        subName: '根据用户对文案风格偏好，选择点击意愿最大的文案进行触达，提升点击率',
      },
      {
        name: '智能指标预测',
        subName: '基于历史数据预测策略效果，实现事前预测事中监控。',
      },
      {
        name: '策略赛马',
        subName: '多策略动态流量分配，根据策略表现及时调整流量，提升流量效率。',
      },
      {
        name: '运营策略助手',
        subName: '基于大模型实现文案生成、策略成效挖掘、策略生成等。',
      },
    ],
  },
  {
    id: 1,
    list: [
      {
        name: '个性化推荐',
        subName: '基于用户画像实现用户个性化推荐，於支持推荐理财、基金、资讯、广告等各类场景。',
      },
      {
        name: '以物推物',
        subName: '基于物品实现相似相关准荐，支持内容带货。',
      },
      {
        name: '热门推荐',
        subName: '支持热门榜单场景，通用场景表现较优。',
      },
    ],
  },
  {
    id: 2,
    list: [
      {
        name: '智能搜索',
        subName: '通过引入自然语言处理、大模型等技术，准确理解客户输入的搜索意图，快速匹配所需的功能、产品、服务或信息，帮助客户高效完成搜索操作，提升用户搜索体验和满意度。',
      },
      {
        name: '个性化搜索',
        subName: '基于客户搜索行为数据、用户画像等信息，实现个性化精准的搜索推荐能力。智能推荐相关功能、产品、服务或资讯，满足客户的介性化、多样化需求，提升客户搜索使用粘性和深度。',
      },
      {
        name: '搜索策略模型可配置',
        subName: '能够结合业务需求变化，通过提供搜索算法策略可配置的产品能力，快速提升对业务需求、客户及市场的响应效率。',
      },
      {
        name: '智能搜索与业务场景结合',
        subName: '将智能搜索功能与业务场景深度结合，通过场景化的搜索服务，提升客户在具体业务操作中的效率和满意度，增强客户对平台的依赖感。',
      },
    ],
  },
  {
    id: 3,
    list: [
      {
        name: 'AI海报',
        subName: '智能海报创作场景，用户选定创作场景后，通过自然语言描述，智能生成多图层海报。',
      },
      {
        name: 'AI绘图',
        subName: '用户通过自然语言生成符合预期的图片。',
      },
      {
        name: 'AI改图',
        subName: '通过自然语言描述，对选定的图片区域进行重绘。',
      },
      {
        name: 'Al抠图',
        subName: '识别用户上传的图片主体，实现自动抠图。',
      },
      {
        name: 'AI扩图',
        subName: '理解用户输入的自然语言，对用户上传的初始图片进行图片延展绘制。',
      },
    ],
  },
]

export default {
  data() {
    return {
      cur: 0,
      tabs: [
        { id: 0, name: '智能主动营销', img: require('../assets/icon1.jpg') },
        { id: 1, name: '智能推荐', img: require('../assets/icon2.jpg') },
        { id: 2, name: '智能搜索', img: require('../assets/icon3.jpg') },
        { id: 3, name: '智能海报', img: require('../assets/icon4.jpg') },
      ],
      tabData: [],
    }
  },
  created() {
    this.tabData = [...datas[0].list]
  },
  methods: {
    onChange(a, b, c) {
      // console.log(a)
    },
    tabChange(index) {
      this.cur = index
      const arr = datas.find(item => item.id === index)
      arr ? this.tabData = [...arr.list] : this.tabData = []
    },
  },
}
</script>

<template>
  <div class="min-w-[1200px]">
    <div class="carousel-box">
      <a-carousel :after-change="onChange" autoplay draggable>
        <div
          class="w-[100%] h-[300px]"
          :style="{ background: `url('${require('../assets/banner1.png')}') no-repeat center center/cover` }"
        />
        <div
          class="w-[100%] h-[300px]"
          :style="{ background: `url('${require('../assets/banner2.png')}') no-repeat center center/cover` }"
        />
      </a-carousel>
    </div>
    <div class="intelligent-center ">
      <h2 class="text-[36px]">
        AI中心 · 企业级营销平台的智能化中心
      </h2>
      <div class="mb-[24]">
        <a-row type="flex" class="bg-[#fff]">
          <a-col
            v-for="(item, index) in tabs"
            :key="index"
            class="tab-item flex justify-center items-center cursor-pointer"
            :span="6"
            :style="{ border: cur === index ? '2px solid #5478ba' : '' }"
            @click="tabChange(index)"
          >
            <img :src="item.img" alt="" class="mr-[8]">
            <span>{{ item.name }}</span>
          </a-col>
        </a-row>
        <a-row type="flex" class="bg-[#fff] mt-[16]">
          <a-col v-for="(item, index) in tabData" :key="index" :span="6" class="p-[12px] pt-[36]">
            <div class="text-[#1776e6] text-[16px]">
              {{ item.name }}
            </div>
            <p class="text-[14px] mt-[12]">
              {{ item.subName }}
            </p>
          </a-col>
        </a-row>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.carousel-box {
  width: 100%;
  overflow: hidden;
  img {
    width: 100%;
  }
}
.intelligent-center {
  width: 75%;
  margin: 0 auto;
  height:535px;

  h2 {
    height: 80px;
    line-height: 85px;
    text-align: center;
  }
  img {
    width: 40px;
  }
  .tab-item {
    height: 80px;
    border: 1px solid #d7d7d7;
    border-right: 0;
    &:last-child {
      border-right: 1px solid #d7d7d7;
    }
  }
}
</style>
