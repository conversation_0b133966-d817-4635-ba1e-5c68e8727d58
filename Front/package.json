{"name": "xingye-static", "type": "module", "version": "1.0.0", "private": true, "homepage": "/aimkt/", "scripts": {"build": "rsbuild build", "dev": "rsbuild dev --open", "preview": "rsbuild preview", "lint": "eslint", "lint:fix": "eslint --ext .js,.vue src --fix"}, "dependencies": {"@ant-design/icons-vue": "^2.0.0", "@antv/g2": "^5.3.3", "@rsbuild/plugin-sass": "^1.3.2", "@types/node": "^24.0.1", "ant-design-vue": "1.x", "lodash": "^4.17.21", "moment": "^2", "vue": "^2.7.16", "vue-clipboard2": "^0.3.3", "vue-json-pretty": "^1.9.5", "vue-router": "^3", "vue-template-compiler": "^2.7.16"}, "devDependencies": {"@antfu/eslint-config": "^5.2.0", "@rsbuild/core": "^1.3.22", "@rsbuild/plugin-vue2": "^1.0.3", "eslint": "^9.32.0", "tailwindcss": "^3", "typescript": "^5.8.3"}}