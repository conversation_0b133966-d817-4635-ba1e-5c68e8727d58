import path from 'node:path'
import { defineConfig } from '@rsbuild/core'
import { pluginSass } from '@rsbuild/plugin-sass'
import { pluginVue2 } from '@rsbuild/plugin-vue2'
import { getBaseUrl } from './src/utils/getEnv.ts'

export default defineConfig({
  source: {
    tsconfigPath: './tsconfig.json',
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      'assets': path.resolve(__dirname, 'src/assets'),
      'wolf-static-cpnt': path.resolve(__dirname, 'src/components/wolf-static-cpnt'),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: true,
    base: getBaseUrl(),
  },
  plugins: [pluginVue2(), pluginSass()],
  html: {
    title: 'AI中心',
    template: './public/index.html',
  },
})
