

## 🚀 项目概述

- **项目名称**: xingye-static
- **技术栈**: Vue 2.7 + TypeScript + Ant Design Vue + TailwindCSS
- **构建工具**: Rsbuild
- **包管理器**: pnpm
- **代码规范**: ESLint + Prettier

## 🛠️ 环境要求

- [Node.js](http://nodejs.cn) >= 16
- [pnpm](https://pnpm.io/zh) >= 7
- [Git](https://git-scm.com/downloads)

## 🚀 快速开始

### 1. 安装依赖

```bash
# 全局安装 pnpm
npm install -g pnpm@7

# 安装项目依赖
pnpm install
```

### 2. 开发环境

```bash
# 启动开发服务器
pnpm dev
```

访问 http://localhost:3000

### 3. 生产构建

```bash
# 构建生产版本
pnpm build
```

### 4. 预览构建结果

```bash
# 预览构建结果
pnpm preview
```

## 📁 项目结构

```
Front/
├── public/                 # 静态资源
│   ├── favicon.ico
│   └── index.html
├── src/                    # 源代码
│   ├── assets/            # 静态资源文件
│   │   ├── *.png          # 图片资源
│   │   └── opp/           # OPP相关资源
│   ├── components/        # 公共组件
│   │   ├── icons/         # 图标组件
│   │   ├── utils/         # 工具组件
│   │   └── wolf-static-cpnt/  # 核心业务组件
│   │       ├── actioncollective/  # 行为集合组件
│   │       ├── event/             # 事件组件
│   │       ├── filter/            # 过滤器组件
│   │       ├── label/             # 标签组件
│   │       ├── segment/           # 分群组件
│   │       └── selectTime/        # 时间选择组件
│   ├── mock/              # Mock 数据
│   ├── pages/             # 页面组件
│   │   ├── frame/         # 框架页面
│   │   ├── home/          # 首页相关
│   │   └── test/          # 测试页面
│   ├── router/            # 路由配置
│   ├── utils/             # 工具函数
│   ├── views/             # 视图组件
│   ├── App.vue            # 根组件
│   ├── index.ts           # 入口文件
│   └── index.css          # 全局样式
├── dist/                  # 构建输出目录
├── docs/                  # 文档目录
├── package.json           # 项目配置
├── pnpm-lock.yaml        # 依赖锁定文件
├── rsbuild.config.ts     # Rsbuild 配置
├── tailwind.config.js    # TailwindCSS 配置
├── tsconfig.json         # TypeScript 配置
├── eslint.config.js      # ESLint 配置
└── README.md             # 项目说明
```

## 🔧 开发工具配置

### VSCode 插件推荐

- [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) - 代码检查
- [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) - 代码格式化
- [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss) - TailwindCSS 智能提示
- [Vue Language Features (Volar)](https://marketplace.visualstudio.com/items?itemName=Vue.volar) - Vue 语言支持

### 代码规范

```bash
# 检查代码规范
pnpm lint

# 自动修复代码规范问题
pnpm lint:fix
```

## 📦 主要依赖

### 核心依赖
- **Vue**: 2.7.16 - 渐进式 JavaScript 框架
- **Vue Router**: 3.x - Vue.js 官方路由管理器
- **Ant Design Vue**: 1.x - 企业级 UI 设计语言和组件库
- **@antv/g2**: 5.3.3 - 数据可视化图形语法

### 工具库
- **lodash**: 4.17.21 - JavaScript 实用工具库
- **moment**: 2.x - 日期处理库
- **vue-clipboard2**: 0.3.3 - 剪贴板操作
- **vue-json-pretty**: 1.9.5 - JSON 美化显示

### 开发工具
- **Rsbuild**: 1.3.22 - 基于 Rspack 的构建工具
- **TypeScript**: 5.8.3 - JavaScript 的超集
- **TailwindCSS**: 3.x - 实用优先的 CSS 框架
- **ESLint**: 9.32.0 - 代码检查工具

## 🌐 部署配置

### 网页全屏模式

在 URL 中添加参数 `fullScreen=true` 启用全屏模式：

```
http://xxx.com/aimkt/home?fullScreen=true
```

### 路径别名

项目配置了以下路径别名：

- `@` → `src/`
- `assets` → `src/assets/`
- `wolf-static-cpnt` → `src/components/wolf-static-cpnt/`

## 🧩 核心组件说明

### wolf-static-cpnt 组件库

这是项目的核心业务组件库，包含以下模块：

- **actioncollective**: 行为集合相关组件
- **event**: 事件处理组件
- **filter**: 数据过滤组件
- **label**: 标签管理组件
- **segment**: 用户分群组件
- **selectTime**: 时间选择组件

### 组件特性

- 全局注册 Ant Design Vue 组件，无需在组件中重复声明
- 支持 SCSS 样式预处理器
- 集成 TailwindCSS 实用类
- TypeScript 类型支持
